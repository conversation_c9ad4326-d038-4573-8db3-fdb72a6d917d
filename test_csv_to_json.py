#!/usr/bin/env python3
"""
Comprehensive test script for csv_to_json.py
Tests all functionality and validates output correctness
"""

import json
import os
import glob
import pandas as pd
from csv_to_json import transform_csv_to_json, batch_transform

def test_dependencies():
    """Test if all required dependencies are available"""
    print("🔍 Testing dependencies...")
    try:
        import pandas as pd
        import json
        import os
        import glob
        print("✅ All dependencies are available")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def test_input_files():
    """Test if input files exist and are readable"""
    print("\n🔍 Testing input files...")
    csv_files = glob.glob("input_csvs/*.csv")
    
    if not csv_files:
        print("❌ No CSV files found in input_csvs/")
        return False
    
    print(f"✅ Found {len(csv_files)} CSV files:")
    for file in csv_files:
        print(f"   - {file}")
        
        # Test if file is readable
        try:
            df = pd.read_csv(file)
            print(f"     📊 {len(df)} rows, {len(df.columns)} columns")
            print(f"     📋 Columns: {list(df.columns)}")
        except Exception as e:
            print(f"     ❌ Error reading file: {e}")
            return False
    
    return True

def test_transformation():
    """Test the transformation process"""
    print("\n🔍 Testing transformation...")
    
    try:
        # Run batch transformation
        batch_transform("input_csvs", "output_jsons")
        print("✅ Batch transformation completed without errors")
        
        # Check output files
        output_files = glob.glob("output_jsons/*.json")
        if not output_files:
            print("❌ No output files generated")
            return False
        
        print(f"✅ Generated {len(output_files)} JSON files:")
        for file in output_files:
            print(f"   - {file}")
        
        return True
    except Exception as e:
        print(f"❌ Transformation failed: {e}")
        return False

def test_output_validity():
    """Test if output JSON files are valid and well-formed"""
    print("\n🔍 Testing output validity...")
    
    output_files = glob.glob("output_jsons/*.json")
    
    for file in output_files:
        print(f"\n📄 Validating {file}:")
        
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("   ✅ Valid JSON format")
            
            # Check structure
            if "messages" not in data:
                print("   ❌ Missing 'messages' key")
                return False
            
            messages = data["messages"]
            print(f"   ✅ Contains {len(messages)} messages")
            
            # Validate message structure
            valid_messages = 0
            for i, msg in enumerate(messages):
                if isinstance(msg, dict):
                    valid_messages += 1
                    # Check for expected fields
                    if "role" in msg and "content" in msg:
                        print(f"   ✅ Message {i+1}: role='{msg['role']}', content length={len(msg.get('content', ''))}")
                else:
                    print(f"   ❌ Message {i+1} is not a dictionary")
            
            print(f"   ✅ {valid_messages}/{len(messages)} messages are valid")
            
        except json.JSONDecodeError as e:
            print(f"   ❌ Invalid JSON: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Error validating file: {e}")
            return False
    
    return True

def test_data_integrity():
    """Test if data is correctly transformed from CSV to JSON"""
    print("\n🔍 Testing data integrity...")
    
    csv_files = glob.glob("input_csvs/*.csv")
    
    for csv_file in csv_files:
        base_name = os.path.splitext(os.path.basename(csv_file))[0]
        json_file = f"output_jsons/{base_name}.json"
        
        if not os.path.exists(json_file):
            print(f"❌ Missing corresponding JSON file for {csv_file}")
            return False
        
        print(f"\n📊 Comparing {csv_file} with {json_file}:")
        
        # Read CSV
        df = pd.read_csv(csv_file)
        
        # Read JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # Compare counts
        csv_rows = len(df)
        json_messages = len(json_data["messages"])
        
        print(f"   📈 CSV rows: {csv_rows}, JSON messages: {json_messages}")
        
        if csv_rows == json_messages:
            print("   ✅ Row count matches")
        else:
            print("   ⚠️  Row count mismatch (this might be expected if some rows are filtered)")
        
        # Check if key fields are preserved
        sample_size = min(3, len(json_data["messages"]))
        for i in range(sample_size):
            csv_row = df.iloc[i]
            json_msg = json_data["messages"][i]
            
            print(f"   🔍 Sample {i+1}:")
            if "role" in df.columns and "role" in json_msg:
                csv_role = str(csv_row["role"]).strip() if not pd.isna(csv_row["role"]) else ""
                json_role = json_msg["role"]
                if csv_role == json_role:
                    print(f"     ✅ Role matches: '{json_role}'")
                else:
                    print(f"     ❌ Role mismatch: CSV='{csv_role}', JSON='{json_role}'")
    
    return True

def main():
    """Run all tests"""
    print("🚀 Starting comprehensive CSV to JSON converter tests...\n")
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Input Files", test_input_files),
        ("Transformation", test_transformation),
        ("Output Validity", test_output_validity),
        ("Data Integrity", test_data_integrity),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 TEST: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ {test_name} test PASSED")
            passed += 1
        else:
            print(f"❌ {test_name} test FAILED")
    
    print(f"\n{'='*50}")
    print(f"📊 FINAL RESULTS: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Your CSV to JSON converter is working correctly!")
        return True
    else:
        print("⚠️  Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    main()
