import pandas as pd
import json
import os
import glob

def create_system_message():
    """Create the standard system message for the conversation"""
    return {
        "role": "system",
        "content": "You are a helpful AI assistant specialized in synthesizing information.\n\nIMPORTANT: Your role is to provide ONLY text-based responses. Do NOT make any tool calls during summary generation.\n\nYour task is to:\n1. Take information from multiple tools that were already executed\n2. Combine and synthesize the information into a coherent response\n3. Answer the user's question directly and comprehensively\n4. Present the information in a natural, conversational way\n\nYou have access to tools but should NOT use them during this final summary phase. TOOLS:\n- current_time(q:str)->{current_time_result}\n- google_trends(q:str)->{google_trends_result}\n- mealdb_food(q:str)->{mealdb_food_result}\n- tmdb_movies(q:str)->{tmdb_movies_result}\n- pubmed(q:str)->{pubmed_result}\n- arxiv_papers(q:str)->{arxiv_papers_result}\n- weather(q:str)->{weather_result}\n- google_places(q:str)->{google_places_result}\n- youtube_summarizer(q:str)->{youtube_summarizer_result}\n- youtube_search(q:str)->{youtube_search_result}\n- calculator(q:str)->{calculator_result}\n- amadeus_travel(q:str)->{amadeus_travel_result}\n- github(q:str)->{github_result}\n- email_sender(q:str)->{email_sender_result}\n- web_search(q:str)->{web_search_result}\n- steam_search(q:str)->{steam_search_result}\n- yahoo_finance(q:str)->{yahoo_finance_result}\n- wikipedia(q:str)->{wikipedia_result}\n- tavily_search(q:str)->{tavily_search_result}\n- multiply(q:str)->{multiply_result}"
    }

def extract_tool_arguments(original_args_str):
    """Extract and format tool arguments from the original_args string"""
    if pd.isna(original_args_str) or not original_args_str:
        return {}

    try:
        args = json.loads(original_args_str)
        # Convert to the format expected in the reference
        if isinstance(args, dict):
            if len(args) == 1 and "query" in args:
                return {"__arg1": args["query"]}
            return args
        return {"__arg1": str(args)}
    except:
        return {"__arg1": str(original_args_str)}

def transform_csv_to_json(input_file, output_dir):
    """Convert conversation log CSV to clean conversation JSON format"""
    df = pd.read_csv(input_file)

    messages = []

    # Add system message first
    messages.append(create_system_message())

    # Process events chronologically to build proper conversation flow
    # Sort all events by timestamp first
    df_sorted = df.sort_values('timestamp')

    # Track conversation state
    turns = {}

    for _, row in df_sorted.iterrows():
        session_id = row.get("session_id", "default")
        turn_id = row.get("turn_id", "default")
        event_type = row.get("event_type", "")
        role = row.get("role", "")
        content = row.get("content", "")
        tool_name = row.get("tool_name", "")
        original_args = row.get("original_args", "")
        execution_result = row.get("execution_result", "")
        timestamp = row.get("timestamp", "")

        # Initialize turn tracking
        turn_key = f"{session_id}_{turn_id}"
        if turn_key not in turns:
            turns[turn_key] = {
                "timestamp": timestamp,
                "user_message": None,
                "reasoning": None,
                "messages": [],  # Store messages in chronological order
                "has_reasoning": False,
                "final_response": None  # Store the final AI response
            }

        turn = turns[turn_key]

        # Skip empty or NaN content
        if pd.isna(content):
            content = ""

        # Capture user messages for this specific turn
        if event_type == "user_message" and role == "user":
            turn["user_message"] = {
                "role": "user",
                "content": str(content).strip()
            }

        # Capture reasoning from reasoning_completed events for this specific turn
        elif event_type == "reasoning_completed" and role == "system":
            if execution_result and not pd.isna(execution_result):
                reasoning_text = str(execution_result).strip()
                if reasoning_text:
                    turn["reasoning"] = reasoning_text

        # Capture tool call interrupts (assistant proposing tool use)
        elif event_type == "tool_call_interrupt" and role == "assistant":
            if tool_name and original_args:
                tool_call = {
                    "role": "assistant"
                }
                # Add reasoning only for the first tool call in this turn (correct order)
                if turn["reasoning"] and not turn["has_reasoning"]:
                    tool_call["reasoning"] = [turn["reasoning"]]
                    turn["has_reasoning"] = True

                # Then add tool_call
                tool_call["tool_call"] = {
                    "name": tool_name,
                    "arguments": extract_tool_arguments(original_args)
                }

                turn["messages"].append(tool_call)

        # Capture tool execution results - add immediately after the tool call
        elif event_type == "tool_execution_approved" and role == "tool":
            if tool_name and execution_result:
                try:
                    # Try to parse execution_result as JSON
                    result_data = json.loads(execution_result) if execution_result else {}
                    tool_response = {
                        "role": "tool",
                        "name": tool_name,
                        "content": json.dumps(result_data, indent=2) if isinstance(result_data, dict) else str(execution_result)
                    }
                    turn["messages"].append(tool_response)
                except:
                    tool_response = {
                        "role": "tool",
                        "name": tool_name,
                        "content": str(execution_result)
                    }
                    turn["messages"].append(tool_response)

        # Capture the final AI response for this turn
        elif event_type == "ai_response" and role == "assistant":
            if content and not pd.isna(content):
                turn["final_response"] = {
                    "role": "assistant",
                    "content": str(content).strip()
                }

    # Process turns in chronological order and build messages
    # Sort by timestamp to ensure proper conversation flow
    sorted_turns = sorted(turns.items(), key=lambda x: x[1]["timestamp"])

    for turn_key, turn in sorted_turns:
        # Add user message if found for this turn
        if turn["user_message"]:
            messages.append(turn["user_message"])

        # Add all messages for this turn (tool calls and responses are already interleaved)
        messages.extend(turn["messages"])

        # Add the final assistant response if available
        if turn["final_response"]:
            messages.append(turn["final_response"])
        # Fallback: Add a generic response if there were tool interactions but no final response
        elif any(msg.get("role") == "tool" for msg in turn["messages"]):
            final_response = {
                "role": "assistant",
                "content": "Based on the analysis of the available data, I can provide insights into your question. The information gathered shows various trends and patterns that help answer your inquiry comprehensively."
            }
            messages.append(final_response)

    # Create output structure
    output = {"messages": messages}

    # Save to file
    os.makedirs(output_dir, exist_ok=True)
    base_name = os.path.splitext(os.path.basename(input_file))[0]
    output_file = os.path.join(output_dir, base_name + ".json")

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(output, f, indent=2, ensure_ascii=False)

    print(f"✅ {input_file} → {output_file}")

    # Count message types
    user_count = len([m for m in messages if m.get('role') == 'user'])
    assistant_count = len([m for m in messages if m.get('role') == 'assistant'])
    tool_count = len([m for m in messages if m.get('role') == 'tool'])
    system_count = len([m for m in messages if m.get('role') == 'system'])

    print(f"   📊 Generated {len(messages)} messages:")
    print(f"      - {system_count} system message(s)")
    print(f"      - {user_count} user message(s)")
    print(f"      - {assistant_count} assistant message(s)")
    print(f"      - {tool_count} tool response(s)")


def batch_transform(input_folder, output_folder):
    """Convert all CSV files in the input folder to JSON"""
    csv_files = glob.glob(os.path.join(input_folder, "*.csv"))

    if not csv_files:
        print(f"❌ No CSV files found in {input_folder}")
        return

    print(f"🔄 Found {len(csv_files)} CSV files to convert...")

    for file in csv_files:
        transform_csv_to_json(file, output_folder)

    print(f"✅ Conversion completed! Check {output_folder} for results.")


# Example usage:
batch_transform("input_csvs", "output_jsons")

