#!/usr/bin/env python3
"""
Automated CSV to JSON System
Integrates file monitoring, conversion, spreadsheet updates, and web interface
"""

import os
import sys
import time
import threading
import logging
from pathlib import Path
from datetime import datetime
import argparse
import json

# Import our modules
from file_monitor import FileMonitor
from spreadsheet_integration import Auto<PERSON><PERSON>p<PERSON>sheet<PERSON>pdater
from link_generator import LinkConfiguration
from web_interface import app

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('automated_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomatedCSVJSONSystem:
    """Main system that coordinates all components"""
    
    def __init__(self, config_file="system_config.json"):
        """Initialize the automated system"""
        self.config_file = Path(config_file)
        self.config = self._load_config()
        
        # Initialize components
        self.file_monitor = None
        self.spreadsheet_updater = None
        self.link_generator = None
        self.web_server_thread = None
        
        # System state
        self.running = False
        
    def _load_config(self):
        """Load system configuration"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_file}")
                return config
            except Exception as e:
                logger.error(f"Error loading config: {e}")
        
        # Return default configuration
        return self._get_default_config()
    
    def _get_default_config(self):
        """Get default system configuration"""
        return {
            "input_dir": "input_csvs",
            "output_dir": "output_jsons",
            "spreadsheet_path": "tracking_spreadsheet.csv",
            "web_interface": {
                "enabled": True,
                "host": "0.0.0.0",
                "port": 5000
            },
            "link_generation": {
                "link_type": "relative",
                "base_url": "",
                "cloud_config": {}
            },
            "monitoring": {
                "process_existing": True,
                "auto_categorize": True
            }
        }
    
    def save_config(self):
        """Save current configuration"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info(f"Saved configuration to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving config: {e}")
            return False
    
    def initialize(self):
        """Initialize all system components"""
        try:
            logger.info("Initializing automated CSV-JSON system...")
            
            # Create directories
            input_dir = Path(self.config["input_dir"])
            output_dir = Path(self.config["output_dir"])
            input_dir.mkdir(exist_ok=True)
            output_dir.mkdir(exist_ok=True)
            
            # Initialize link generator
            link_config = LinkConfiguration()
            link_config.config.update(self.config["link_generation"])
            self.link_generator = link_config.get_generator()
            
            # Initialize spreadsheet updater
            spreadsheet_path = self.config["spreadsheet_path"]
            self.spreadsheet_updater = AutomatedSpreadsheetUpdater(spreadsheet_path)
            
            if not self.spreadsheet_updater.initialize():
                logger.error("Failed to initialize spreadsheet updater")
                return False
            
            # Initialize file monitor with callback
            self.file_monitor = FileMonitor(
                input_dir=input_dir,
                output_dir=output_dir,
                callback=self._file_processed_callback
            )
            
            logger.info("System initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            return False
    
    def _file_processed_callback(self, csv_file, json_file):
        """Callback function called when a file is processed"""
        try:
            logger.info(f"Processing callback for {csv_file.name}")
            
            # Extract metadata from filename or use defaults
            annotator = self._extract_annotator(csv_file)
            category = self._extract_category(csv_file)
            
            # Update spreadsheet
            success = self.spreadsheet_updater.process_new_files(
                csv_file, json_file, annotator, category
            )
            
            if success:
                logger.info(f"Spreadsheet updated for {csv_file.name}")
            else:
                logger.error(f"Failed to update spreadsheet for {csv_file.name}")
                
        except Exception as e:
            logger.error(f"Error in file processed callback: {e}")
    
    def _extract_annotator(self, csv_file):
        """Extract annotator name from filename or metadata"""
        # Try to extract from filename patterns
        filename = csv_file.stem
        
        # Look for common patterns like "annotator_name_timestamp"
        parts = filename.split('_')
        if len(parts) >= 2:
            # Check if second part looks like a name (not timestamp/uuid)
            potential_name = parts[1]
            if not potential_name.isdigit() and len(potential_name) > 2:
                return potential_name
        
        return "Unknown"
    
    def _extract_category(self, csv_file):
        """Extract category from filename or use auto-categorization"""
        filename = csv_file.stem.lower()
        
        # Auto-categorize based on filename patterns
        if 'chat' in filename or 'conversation' in filename:
            return 'conversation_logs'
        elif 'tool' in filename or 'trajectory' in filename:
            return 'tool_trajectories'
        elif 'user' in filename or 'interaction' in filename:
            return 'user_interactions'
        else:
            return 'other'
    
    def start_file_monitoring(self):
        """Start the file monitoring system"""
        try:
            logger.info("Starting file monitoring...")
            
            # Process existing files if configured
            if self.config["monitoring"]["process_existing"]:
                self.file_monitor.process_existing_files()
            
            # Start monitoring
            self.file_monitor.start()
            logger.info("File monitoring started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start file monitoring: {e}")
            return False
    
    def start_web_interface(self):
        """Start the web interface in a separate thread"""
        if not self.config["web_interface"]["enabled"]:
            logger.info("Web interface disabled in configuration")
            return True
            
        try:
            logger.info("Starting web interface...")
            
            def run_web_server():
                app.run(
                    host=self.config["web_interface"]["host"],
                    port=self.config["web_interface"]["port"],
                    debug=False,
                    use_reloader=False
                )
            
            self.web_server_thread = threading.Thread(target=run_web_server, daemon=True)
            self.web_server_thread.start()
            
            logger.info(f"Web interface started on http://{self.config['web_interface']['host']}:{self.config['web_interface']['port']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start web interface: {e}")
            return False
    
    def start(self):
        """Start the complete automated system"""
        try:
            logger.info("🚀 Starting Automated CSV-JSON System")
            
            if not self.initialize():
                logger.error("System initialization failed")
                return False
            
            # Start file monitoring
            if not self.start_file_monitoring():
                logger.error("File monitoring failed to start")
                return False
            
            # Start web interface
            if not self.start_web_interface():
                logger.error("Web interface failed to start")
                return False
            
            self.running = True
            logger.info("✅ Automated system started successfully")
            
            # Print status
            self._print_status()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start system: {e}")
            return False
    
    def stop(self):
        """Stop the automated system"""
        try:
            logger.info("Stopping automated system...")
            
            self.running = False
            
            # Stop file monitoring
            if self.file_monitor:
                self.file_monitor.stop()
            
            # Web server will stop when main thread exits
            
            logger.info("✅ Automated system stopped")
            
        except Exception as e:
            logger.error(f"Error stopping system: {e}")
    
    def _print_status(self):
        """Print current system status"""
        print("\n" + "="*60)
        print("🔄 AUTOMATED CSV-JSON SYSTEM STATUS")
        print("="*60)
        print(f"📁 Input Directory: {Path(self.config['input_dir']).absolute()}")
        print(f"📄 Output Directory: {Path(self.config['output_dir']).absolute()}")
        print(f"📊 Spreadsheet: {Path(self.config['spreadsheet_path']).absolute()}")
        
        if self.config["web_interface"]["enabled"]:
            print(f"🌐 Web Interface: http://{self.config['web_interface']['host']}:{self.config['web_interface']['port']}")
        
        print(f"🔗 Link Type: {self.config['link_generation']['link_type']}")
        print("="*60)
        print("📋 INSTRUCTIONS FOR ANNOTATORS:")
        print("1. Upload CSV files via web interface OR")
        print("2. Copy CSV files to the input directory")
        print("3. JSON files will be generated automatically")
        print("4. Spreadsheet will be updated with file links")
        print("="*60)
        print("Press Ctrl+C to stop the system")
        print()

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Automated CSV to JSON System")
    parser.add_argument("--config", default="system_config.json", help="Configuration file path")
    parser.add_argument("--create-config", action="store_true", help="Create sample configuration file")
    
    args = parser.parse_args()
    
    if args.create_config:
        # Create sample configuration
        system = AutomatedCSVJSONSystem(args.config)
        system.save_config()
        print(f"✅ Created sample configuration: {args.config}")
        return
    
    # Start the system
    system = AutomatedCSVJSONSystem(args.config)
    
    try:
        if system.start():
            # Keep running until interrupted
            while system.running:
                time.sleep(1)
        else:
            print("❌ Failed to start system")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Received interrupt signal")
        system.stop()
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
