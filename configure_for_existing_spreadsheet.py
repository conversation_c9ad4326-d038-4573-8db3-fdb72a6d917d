#!/usr/bin/env python3
"""
Configuration script for setting up the monitor with your existing spreadsheet
"""

import pandas as pd
import json
from pathlib import Path

def analyze_spreadsheet(spreadsheet_path):
    """Analyze your existing spreadsheet structure"""
    print(f"📊 Analyzing spreadsheet: {spreadsheet_path}")
    
    try:
        # Try to read the spreadsheet
        if spreadsheet_path.endswith('.csv'):
            df = pd.read_csv(spreadsheet_path)
        elif spreadsheet_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(spreadsheet_path)
        else:
            print("❌ Unsupported file format. Please use .csv, .xlsx, or .xls")
            return None
        
        print(f"✅ Successfully loaded spreadsheet with {len(df)} rows")
        print(f"📋 Found {len(df.columns)} columns:")
        
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")
        
        return df.columns.tolist()
        
    except Exception as e:
        print(f"❌ Error reading spreadsheet: {e}")
        return None

def create_column_mapping(columns):
    """Create mapping between your columns and required columns"""
    print("\n🔧 Column Mapping Setup")
    print("=" * 40)
    
    required_mappings = {
        'id_column': 'ID column (unique identifier for each row)',
        'csv_column': 'CSV file path column',
        'json_column': 'JSON file path column (can be empty initially)'
    }
    
    optional_mappings = {
        'date_column': 'Date column (optional)',
        'annotator_column': 'Annotator/User column (optional)',
        'category_column': 'Category column (optional)',
        'status_column': 'Status column (optional)'
    }
    
    mapping = {}
    
    print("📋 Available columns:")
    for i, col in enumerate(columns, 1):
        print(f"   {i}. {col}")
    
    print("\n🔧 Required Mappings:")
    for key, description in required_mappings.items():
        while True:
            print(f"\n{description}")
            choice = input(f"Enter column number or name for {key}: ").strip()
            
            # Try to parse as number
            try:
                col_index = int(choice) - 1
                if 0 <= col_index < len(columns):
                    mapping[key] = columns[col_index]
                    print(f"✅ Mapped {key} to '{columns[col_index]}'")
                    break
                else:
                    print("❌ Invalid column number")
            except ValueError:
                # Try to find by name
                matching_cols = [col for col in columns if choice.lower() in col.lower()]
                if len(matching_cols) == 1:
                    mapping[key] = matching_cols[0]
                    print(f"✅ Mapped {key} to '{matching_cols[0]}'")
                    break
                elif len(matching_cols) > 1:
                    print(f"❌ Multiple matches found: {matching_cols}")
                    print("Please be more specific")
                else:
                    print("❌ Column not found. Please try again.")
    
    print("\n🔧 Optional Mappings (press Enter to skip):")
    for key, description in optional_mappings.items():
        choice = input(f"{description} (Enter to skip): ").strip()
        
        if choice:
            try:
                col_index = int(choice) - 1
                if 0 <= col_index < len(columns):
                    mapping[key] = columns[col_index]
                    print(f"✅ Mapped {key} to '{columns[col_index]}'")
                else:
                    print("❌ Invalid column number, skipping")
            except ValueError:
                matching_cols = [col for col in columns if choice.lower() in col.lower()]
                if len(matching_cols) == 1:
                    mapping[key] = matching_cols[0]
                    print(f"✅ Mapped {key} to '{matching_cols[0]}'")
                elif len(matching_cols) > 1:
                    print(f"❌ Multiple matches found: {matching_cols}, skipping")
                else:
                    print("❌ Column not found, skipping")
    
    return mapping

def create_custom_monitor_config(spreadsheet_path, column_mapping):
    """Create a custom configuration file"""
    config = {
        'spreadsheet_path': spreadsheet_path,
        'input_dir': 'input_csvs',
        'output_dir': 'output_jsons',
        'check_interval': 10,
        'column_mapping': column_mapping,
        'auto_download': True,
        'base_url': ''
    }
    
    config_file = 'custom_monitor_config.json'
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n✅ Created configuration file: {config_file}")
    return config_file

def create_custom_monitor_script(config_file):
    """Create a custom monitor script for your spreadsheet"""
    
    script_content = f'''#!/usr/bin/env python3
"""
Custom spreadsheet monitor for your existing spreadsheet
Auto-generated configuration script
"""

import pandas as pd
import time
import os
import json
from pathlib import Path
from csv_to_json import transform_csv_to_json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class CustomSpreadsheetMonitor:
    """Custom monitor for your existing spreadsheet"""
    
    def __init__(self, config_file="{config_file}"):
        # Load configuration
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        self.spreadsheet_path = Path(self.config['spreadsheet_path'])
        self.column_mapping = self.config['column_mapping']
        self.processed_ids = set()
        
        # Create output directory
        self.output_dir = Path(self.config['output_dir'])
        self.output_dir.mkdir(exist_ok=True)
        
        # Required columns
        self.id_col = self.column_mapping['id_column']
        self.csv_col = self.column_mapping['csv_column']
        self.json_col = self.column_mapping['json_column']
        
        logger.info(f"Initialized monitor for: {{self.spreadsheet_path}}")
        logger.info(f"ID column: {{self.id_col}}")
        logger.info(f"CSV column: {{self.csv_col}}")
        logger.info(f"JSON column: {{self.json_col}}")
    
    def load_spreadsheet(self):
        """Load your spreadsheet"""
        try:
            if not self.spreadsheet_path.exists():
                logger.error(f"Spreadsheet not found: {{self.spreadsheet_path}}")
                return None
            
            if self.spreadsheet_path.suffix.lower() == '.csv':
                df = pd.read_csv(self.spreadsheet_path)
            else:
                df = pd.read_excel(self.spreadsheet_path)
            
            logger.info(f"Loaded spreadsheet with {{len(df)}} rows")
            return df
            
        except Exception as e:
            logger.error(f"Error loading spreadsheet: {{e}}")
            return None
    
    def save_spreadsheet(self, df):
        """Save your spreadsheet"""
        try:
            if self.spreadsheet_path.suffix.lower() == '.csv':
                df.to_csv(self.spreadsheet_path, index=False)
            else:
                df.to_excel(self.spreadsheet_path, index=False)
            
            logger.info(f"Saved spreadsheet: {{self.spreadsheet_path}}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving spreadsheet: {{e}}")
            return False
    
    def check_and_process(self):
        """Check spreadsheet for new entries and process them"""
        try:
            df = self.load_spreadsheet()
            if df is None:
                return
            
            # Check if required columns exist
            missing_cols = []
            for col in [self.id_col, self.csv_col]:
                if col not in df.columns:
                    missing_cols.append(col)
            
            if missing_cols:
                logger.error(f"Missing required columns: {{missing_cols}}")
                return
            
            # Add JSON column if it doesn't exist
            if self.json_col not in df.columns:
                df[self.json_col] = ''
                logger.info(f"Added missing JSON column: {{self.json_col}}")
            
            # Process each row
            updated = False
            for index, row in df.iterrows():
                entry_id = str(row[self.id_col])
                csv_link = str(row[self.csv_col]) if pd.notna(row[self.csv_col]) else ''
                json_link = str(row[self.json_col]) if pd.notna(row[self.json_col]) else ''
                
                # Skip if already processed or no CSV link
                if entry_id in self.processed_ids or not csv_link or csv_link == 'nan':
                    continue
                
                # Skip if JSON link already exists
                if json_link and json_link != 'nan' and json_link.strip():
                    self.processed_ids.add(entry_id)
                    continue
                
                # Process this entry
                logger.info(f"🔄 Processing entry: {{entry_id}}")
                
                # Get CSV file path
                csv_path = Path(csv_link)
                
                # Check if CSV file exists
                if not csv_path.exists():
                    logger.warning(f"CSV file not found: {{csv_path}}")
                    continue
                
                try:
                    # Convert CSV to JSON
                    transform_csv_to_json(str(csv_path), str(self.output_dir))
                    
                    # Generate JSON file path
                    json_filename = csv_path.stem + '.json'
                    json_path = self.output_dir / json_filename
                    
                    if json_path.exists():
                        # Update the spreadsheet with JSON link
                        json_link = f"{{self.config['output_dir']}}/{{json_filename}}"
                        df.at[index, self.json_col] = json_link
                        
                        logger.info(f"✅ Created JSON: {{json_link}}")
                        updated = True
                        self.processed_ids.add(entry_id)
                    else:
                        logger.error(f"❌ JSON file not created for: {{entry_id}}")
                        
                except Exception as e:
                    logger.error(f"❌ Error processing {{entry_id}}: {{e}}")
            
            # Save updated spreadsheet
            if updated:
                self.save_spreadsheet(df)
                logger.info("📊 Spreadsheet updated with new JSON links")
                
        except Exception as e:
            logger.error(f"❌ Error checking spreadsheet: {{e}}")
    
    def run_continuous(self, interval=None):
        """Run continuous monitoring"""
        interval = interval or self.config['check_interval']
        
        logger.info("🔄 Starting continuous monitoring...")
        logger.info(f"📊 Watching: {{self.spreadsheet_path}}")
        logger.info(f"📁 JSON output: {{self.output_dir}}")
        logger.info(f"⏱️ Check interval: {{interval}} seconds")
        logger.info("Press Ctrl+C to stop")
        
        try:
            while True:
                self.check_and_process()
                time.sleep(interval)
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring stopped")
    
    def run_once(self):
        """Run once and exit"""
        logger.info("🔄 Processing spreadsheet once...")
        self.check_and_process()
        logger.info("✅ Processing complete")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Custom Spreadsheet Monitor")
    parser.add_argument("--continuous", "-c", action="store_true", help="Run continuous monitoring")
    parser.add_argument("--interval", "-i", type=int, default=10, help="Check interval in seconds")
    
    args = parser.parse_args()
    
    monitor = CustomSpreadsheetMonitor()
    
    print("📊 Custom Spreadsheet Monitor")
    print("=" * 35)
    print(f"📋 Spreadsheet: {{monitor.spreadsheet_path}}")
    print(f"📁 JSON output: {{monitor.output_dir}}")
    print()
    
    if args.continuous:
        monitor.run_continuous(args.interval)
    else:
        monitor.run_once()

if __name__ == "__main__":
    main()
'''
    
    script_file = 'custom_spreadsheet_monitor.py'
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    print(f"✅ Created custom monitor script: {script_file}")
    return script_file

def main():
    """Main setup function"""
    print("🔧 Setting Up Monitor for Your Existing Spreadsheet")
    print("=" * 55)
    
    # Get spreadsheet path
    spreadsheet_path = input("📊 Enter path to your existing spreadsheet: ").strip()
    
    if not Path(spreadsheet_path).exists():
        print(f"❌ File not found: {spreadsheet_path}")
        return
    
    # Analyze spreadsheet
    columns = analyze_spreadsheet(spreadsheet_path)
    if not columns:
        return
    
    # Create column mapping
    column_mapping = create_column_mapping(columns)
    
    # Create configuration
    config_file = create_custom_monitor_config(spreadsheet_path, column_mapping)
    
    # Create custom monitor script
    script_file = create_custom_monitor_script(config_file)
    
    print("\n🎉 Setup Complete!")
    print("=" * 20)
    print(f"✅ Configuration: {config_file}")
    print(f"✅ Monitor script: {script_file}")
    print()
    print("🚀 To start monitoring:")
    print(f"   python {script_file} --continuous")
    print()
    print("📋 To process once:")
    print(f"   python {script_file}")

if __name__ == "__main__":
    main()
