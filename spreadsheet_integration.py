#!/usr/bin/env python3
"""
Spreadsheet integration module for automatic CSV/JSON link updates
Supports Google Sheets, Excel files, and CSV spreadsheets
"""

import os
import pandas as pd
from datetime import datetime
from pathlib import Path
import logging
from typing import Optional, Dict, Any
import json

logger = logging.getLogger(__name__)

class SpreadsheetManager:
    """Manages spreadsheet operations for tracking CSV/JSON files"""
    
    def __init__(self, spreadsheet_path: str, spreadsheet_type: str = "auto"):
        """
        Initialize spreadsheet manager
        
        Args:
            spreadsheet_path: Path to the spreadsheet file
            spreadsheet_type: Type of spreadsheet ('excel', 'csv', 'google', 'auto')
        """
        self.spreadsheet_path = Path(spreadsheet_path)
        self.spreadsheet_type = self._detect_type(spreadsheet_type)
        self.df = None
        self.required_columns = [
            'Date', 'ID', 'Annotator', 'Link to CSV', 'Link to JSON', 'Category'
        ]
        
    def _detect_type(self, spreadsheet_type: str) -> str:
        """Auto-detect spreadsheet type based on file extension"""
        if spreadsheet_type != "auto":
            return spreadsheet_type
            
        suffix = self.spreadsheet_path.suffix.lower()
        if suffix in ['.xlsx', '.xls']:
            return 'excel'
        elif suffix == '.csv':
            return 'csv'
        else:
            return 'csv'  # Default to CSV
    
    def load_spreadsheet(self) -> bool:
        """Load the spreadsheet into memory"""
        try:
            if not self.spreadsheet_path.exists():
                logger.info(f"Spreadsheet not found, creating new one: {self.spreadsheet_path}")
                self._create_new_spreadsheet()
                return True
                
            if self.spreadsheet_type == 'excel':
                self.df = pd.read_excel(self.spreadsheet_path)
            elif self.spreadsheet_type == 'csv':
                self.df = pd.read_csv(self.spreadsheet_path)
            else:
                raise ValueError(f"Unsupported spreadsheet type: {self.spreadsheet_type}")
                
            # Ensure required columns exist
            self._ensure_columns()
            logger.info(f"Loaded spreadsheet with {len(self.df)} rows")
            return True
            
        except Exception as e:
            logger.error(f"Error loading spreadsheet: {e}")
            return False
    
    def _create_new_spreadsheet(self):
        """Create a new spreadsheet with required columns"""
        self.df = pd.DataFrame(columns=self.required_columns)
        self.save_spreadsheet()
        logger.info("Created new spreadsheet with required columns")
    
    def _ensure_columns(self):
        """Ensure all required columns exist in the spreadsheet"""
        for col in self.required_columns:
            if col not in self.df.columns:
                self.df[col] = ''
                logger.info(f"Added missing column: {col}")
    
    def save_spreadsheet(self) -> bool:
        """Save the spreadsheet to file"""
        try:
            # Create directory if it doesn't exist
            self.spreadsheet_path.parent.mkdir(parents=True, exist_ok=True)
            
            if self.spreadsheet_type == 'excel':
                self.df.to_excel(self.spreadsheet_path, index=False)
            elif self.spreadsheet_type == 'csv':
                self.df.to_csv(self.spreadsheet_path, index=False)
                
            logger.info(f"Saved spreadsheet: {self.spreadsheet_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving spreadsheet: {e}")
            return False
    
    def add_file_entry(self, csv_file: Path, json_file: Path, 
                      annotator: str = "", category: str = "") -> bool:
        """
        Add a new entry for CSV/JSON file pair
        
        Args:
            csv_file: Path to CSV file
            json_file: Path to JSON file
            annotator: Name of the annotator
            category: Category for the entry
        """
        try:
            # Generate unique ID based on filename and timestamp
            file_id = f"{csv_file.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create file links (relative paths or URLs)
            csv_link = self._generate_file_link(csv_file)
            json_link = self._generate_file_link(json_file)
            
            # Create new row
            new_row = {
                'Date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'ID': file_id,
                'Annotator': annotator,
                'Link to CSV': csv_link,
                'Link to JSON': json_link,
                'Category': category
            }
            
            # Add any additional columns that exist in the spreadsheet
            for col in self.df.columns:
                if col not in new_row:
                    new_row[col] = ''
            
            # Append to dataframe
            self.df = pd.concat([self.df, pd.DataFrame([new_row])], ignore_index=True)
            
            logger.info(f"Added entry for {csv_file.name} -> {json_file.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding file entry: {e}")
            return False
    
    def update_existing_entry(self, csv_file: Path, json_file: Path) -> bool:
        """Update an existing entry if CSV file already exists in spreadsheet"""
        try:
            csv_name = csv_file.name
            
            # Find existing entry by CSV filename
            mask = self.df['Link to CSV'].str.contains(csv_name, na=False)
            
            if mask.any():
                # Update the JSON link for existing entry
                json_link = self._generate_file_link(json_file)
                self.df.loc[mask, 'Link to JSON'] = json_link
                self.df.loc[mask, 'Date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                logger.info(f"Updated existing entry for {csv_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating existing entry: {e}")
            return False
    
    def _generate_file_link(self, file_path: Path) -> str:
        """Generate a file link (can be customized for different hosting solutions)"""
        # For now, return relative path
        # This can be modified to generate URLs for web hosting
        return str(file_path.relative_to(Path.cwd()))
    
    def find_entry_by_csv(self, csv_file: Path) -> Optional[Dict[str, Any]]:
        """Find an entry by CSV filename"""
        try:
            csv_name = csv_file.name
            mask = self.df['Link to CSV'].str.contains(csv_name, na=False)
            
            if mask.any():
                return self.df[mask].iloc[0].to_dict()
            return None
            
        except Exception as e:
            logger.error(f"Error finding entry: {e}")
            return None
    
    def get_stats(self) -> Dict[str, int]:
        """Get statistics about the spreadsheet"""
        if self.df is None:
            return {}
            
        stats = {
            'total_entries': len(self.df),
            'entries_with_csv': len(self.df[self.df['Link to CSV'].notna() & (self.df['Link to CSV'] != '')]),
            'entries_with_json': len(self.df[self.df['Link to JSON'].notna() & (self.df['Link to JSON'] != '')]),
            'unique_annotators': len(self.df['Annotator'].dropna().unique()) if 'Annotator' in self.df.columns else 0
        }
        
        return stats

class AutomatedSpreadsheetUpdater:
    """Automated updater that integrates with file monitoring"""
    
    def __init__(self, spreadsheet_path: str, base_url: str = ""):
        """
        Initialize automated updater
        
        Args:
            spreadsheet_path: Path to the spreadsheet
            base_url: Base URL for generating file links (optional)
        """
        self.manager = SpreadsheetManager(spreadsheet_path)
        self.base_url = base_url.rstrip('/')
        
    def initialize(self) -> bool:
        """Initialize the spreadsheet manager"""
        return self.manager.load_spreadsheet()
    
    def process_new_files(self, csv_file: Path, json_file: Path, 
                         annotator: str = "", category: str = "") -> bool:
        """
        Process new CSV/JSON file pair and update spreadsheet
        
        This is the callback function used by the file monitor
        """
        try:
            # Check if entry already exists
            existing = self.manager.find_entry_by_csv(csv_file)
            
            if existing:
                # Update existing entry
                success = self.manager.update_existing_entry(csv_file, json_file)
            else:
                # Add new entry
                success = self.manager.add_file_entry(csv_file, json_file, annotator, category)
            
            if success:
                # Save the spreadsheet
                self.manager.save_spreadsheet()
                logger.info(f"Spreadsheet updated for {csv_file.name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error processing files for spreadsheet: {e}")
            return False

def main():
    """Test the spreadsheet integration"""
    # Example usage
    updater = AutomatedSpreadsheetUpdater("tracking_spreadsheet.csv")
    
    if updater.initialize():
        print("✅ Spreadsheet initialized successfully")
        
        # Print stats
        stats = updater.manager.get_stats()
        print(f"📊 Spreadsheet stats: {stats}")
    else:
        print("❌ Failed to initialize spreadsheet")

if __name__ == "__main__":
    main()
