#!/usr/bin/env python3
"""
Link generation system for CSV and JSON files
Supports local file paths, web URLs, and cloud storage links
"""

import os
from pathlib import Path
from urllib.parse import urljoin, quote
import logging
from typing import Optional, Dict, Any
import json

logger = logging.getLogger(__name__)

class LinkGenerator:
    """Generates various types of links for CSV and JSON files"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize link generator with configuration
        
        Args:
            config: Configuration dictionary with link generation settings
        """
        self.config = config or {}
        self.base_url = self.config.get('base_url', '')
        self.link_type = self.config.get('link_type', 'relative')  # relative, absolute, web, cloud
        self.cloud_config = self.config.get('cloud_config', {})
        
    def generate_csv_link(self, csv_file: Path) -> str:
        """Generate link for CSV file"""
        return self._generate_link(csv_file, 'csv')
    
    def generate_json_link(self, json_file: Path) -> str:
        """Generate link for JSON file"""
        return self._generate_link(json_file, 'json')
    
    def _generate_link(self, file_path: Path, file_type: str) -> str:
        """Generate link based on configuration"""
        try:
            if self.link_type == 'relative':
                return self._generate_relative_link(file_path)
            elif self.link_type == 'absolute':
                return self._generate_absolute_link(file_path)
            elif self.link_type == 'web':
                return self._generate_web_link(file_path)
            elif self.link_type == 'cloud':
                return self._generate_cloud_link(file_path, file_type)
            else:
                return self._generate_relative_link(file_path)
                
        except Exception as e:
            logger.error(f"Error generating link for {file_path}: {e}")
            return str(file_path)
    
    def _generate_relative_link(self, file_path: Path) -> str:
        """Generate relative file path link"""
        try:
            # Get relative path from current working directory
            relative_path = file_path.relative_to(Path.cwd())
            return str(relative_path).replace('\\', '/')
        except ValueError:
            # If file is not relative to cwd, return absolute path
            return str(file_path).replace('\\', '/')
    
    def _generate_absolute_link(self, file_path: Path) -> str:
        """Generate absolute file path link"""
        return str(file_path.absolute()).replace('\\', '/')
    
    def _generate_web_link(self, file_path: Path) -> str:
        """Generate web URL link"""
        if not self.base_url:
            logger.warning("No base_url configured for web links, falling back to relative")
            return self._generate_relative_link(file_path)
        
        # Get relative path and URL encode it
        try:
            relative_path = file_path.relative_to(Path.cwd())
            encoded_path = quote(str(relative_path).replace('\\', '/'))
            return urljoin(self.base_url + '/', encoded_path)
        except ValueError:
            # If file is not relative to cwd, use filename only
            encoded_filename = quote(file_path.name)
            return urljoin(self.base_url + '/', encoded_filename)
    
    def _generate_cloud_link(self, file_path: Path, file_type: str) -> str:
        """Generate cloud storage link (placeholder for cloud integration)"""
        cloud_type = self.cloud_config.get('type', 'aws')
        
        if cloud_type == 'aws':
            return self._generate_aws_s3_link(file_path, file_type)
        elif cloud_type == 'gcp':
            return self._generate_gcp_storage_link(file_path, file_type)
        elif cloud_type == 'azure':
            return self._generate_azure_blob_link(file_path, file_type)
        else:
            logger.warning(f"Unsupported cloud type: {cloud_type}")
            return self._generate_web_link(file_path)
    
    def _generate_aws_s3_link(self, file_path: Path, file_type: str) -> str:
        """Generate AWS S3 link"""
        bucket = self.cloud_config.get('bucket', 'your-bucket')
        region = self.cloud_config.get('region', 'us-east-1')
        folder = self.cloud_config.get('folder', file_type + 's')
        
        # Generate S3 URL
        s3_key = f"{folder}/{file_path.name}"
        s3_url = f"https://{bucket}.s3.{region}.amazonaws.com/{s3_key}"
        
        return s3_url
    
    def _generate_gcp_storage_link(self, file_path: Path, file_type: str) -> str:
        """Generate Google Cloud Storage link"""
        bucket = self.cloud_config.get('bucket', 'your-bucket')
        folder = self.cloud_config.get('folder', file_type + 's')
        
        # Generate GCS URL
        gcs_key = f"{folder}/{file_path.name}"
        gcs_url = f"https://storage.googleapis.com/{bucket}/{gcs_key}"
        
        return gcs_url
    
    def _generate_azure_blob_link(self, file_path: Path, file_type: str) -> str:
        """Generate Azure Blob Storage link"""
        account = self.cloud_config.get('account', 'your-account')
        container = self.cloud_config.get('container', 'your-container')
        folder = self.cloud_config.get('folder', file_type + 's')
        
        # Generate Azure Blob URL
        blob_key = f"{folder}/{file_path.name}"
        blob_url = f"https://{account}.blob.core.windows.net/{container}/{blob_key}"
        
        return blob_url

class LinkConfiguration:
    """Manages link generation configuration"""
    
    def __init__(self, config_file: str = "link_config.json"):
        """Initialize with configuration file"""
        self.config_file = Path(config_file)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info(f"Loaded link configuration from {self.config_file}")
                return config
            except Exception as e:
                logger.error(f"Error loading config: {e}")
        
        # Return default configuration
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "link_type": "relative",
            "base_url": "",
            "cloud_config": {
                "type": "aws",
                "bucket": "your-bucket",
                "region": "us-east-1",
                "folder": "files"
            }
        }
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info(f"Saved configuration to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving config: {e}")
            return False
    
    def update_config(self, **kwargs) -> bool:
        """Update configuration parameters"""
        try:
            self.config.update(kwargs)
            return self.save_config()
        except Exception as e:
            logger.error(f"Error updating config: {e}")
            return False
    
    def get_generator(self) -> LinkGenerator:
        """Get a configured link generator"""
        return LinkGenerator(self.config)

def create_sample_config():
    """Create a sample configuration file"""
    config = LinkConfiguration()
    
    # Example configurations for different scenarios
    examples = {
        "local_development": {
            "link_type": "relative",
            "base_url": "",
            "cloud_config": {}
        },
        "web_server": {
            "link_type": "web",
            "base_url": "https://your-domain.com/files",
            "cloud_config": {}
        },
        "aws_s3": {
            "link_type": "cloud",
            "base_url": "",
            "cloud_config": {
                "type": "aws",
                "bucket": "your-csv-json-bucket",
                "region": "us-east-1",
                "folder": "trajectories"
            }
        }
    }
    
    # Save example configurations
    for name, example_config in examples.items():
        config_file = f"link_config_{name}.json"
        with open(config_file, 'w') as f:
            json.dump(example_config, f, indent=2)
        print(f"Created example config: {config_file}")

def main():
    """Test the link generator"""
    # Create sample configurations
    create_sample_config()
    
    # Test with default configuration
    config = LinkConfiguration()
    generator = config.get_generator()
    
    # Test file paths
    csv_file = Path("input_csvs/test.csv")
    json_file = Path("output_jsons/test.json")
    
    print("🔗 Testing link generation:")
    print(f"CSV Link: {generator.generate_csv_link(csv_file)}")
    print(f"JSON Link: {generator.generate_json_link(json_file)}")
    
    # Test different configurations
    print("\n🔗 Testing different link types:")
    
    for link_type in ['relative', 'absolute', 'web']:
        config.update_config(link_type=link_type, base_url="https://example.com/files")
        gen = config.get_generator()
        print(f"{link_type.title()}: {gen.generate_csv_link(csv_file)}")

if __name__ == "__main__":
    main()
