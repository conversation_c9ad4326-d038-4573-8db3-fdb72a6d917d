#!/usr/bin/env python3
"""
File monitoring system for automatic CSV to JSON conversion
Watches for new CSV files and automatically processes them
"""

import os
import time
import threading
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from csv_to_json import transform_csv_to_json
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('file_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CSVFileHandler(FileSystemEventHandler):
    """Handler for CSV file events"""
    
    def __init__(self, input_dir, output_dir, callback=None):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.callback = callback
        self.processing = set()  # Track files being processed
        
    def on_created(self, event):
        """Handle file creation events"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path)
        if file_path.suffix.lower() == '.csv':
            logger.info(f"New CSV file detected: {file_path}")
            # Add a small delay to ensure file is fully written
            threading.Timer(2.0, self.process_file, args=[file_path]).start()
    
    def on_moved(self, event):
        """Handle file move events (like drag and drop)"""
        if event.is_directory:
            return
            
        dest_path = Path(event.dest_path)
        if dest_path.suffix.lower() == '.csv':
            logger.info(f"CSV file moved to watched directory: {dest_path}")
            threading.Timer(2.0, self.process_file, args=[dest_path]).start()
    
    def process_file(self, file_path):
        """Process a CSV file"""
        file_path = Path(file_path)
        
        # Avoid processing the same file multiple times
        if str(file_path) in self.processing:
            return
            
        self.processing.add(str(file_path))
        
        try:
            # Check if file exists and is readable
            if not file_path.exists():
                logger.warning(f"File no longer exists: {file_path}")
                return
                
            # Wait a bit more to ensure file is completely written
            time.sleep(1)
            
            # Check file size to ensure it's not empty
            if file_path.stat().st_size == 0:
                logger.warning(f"File is empty: {file_path}")
                return
                
            logger.info(f"Processing CSV file: {file_path}")
            
            # Convert CSV to JSON
            transform_csv_to_json(str(file_path), str(self.output_dir))
            
            # Call callback if provided (for spreadsheet updates)
            if self.callback:
                json_file = self.output_dir / f"{file_path.stem}.json"
                self.callback(file_path, json_file)
                
            logger.info(f"Successfully processed: {file_path}")
            
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
        finally:
            self.processing.discard(str(file_path))

class FileMonitor:
    """Main file monitoring class"""
    
    def __init__(self, input_dir="input_csvs", output_dir="output_jsons", callback=None):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.callback = callback
        self.observer = None
        self.handler = None
        
        # Create directories if they don't exist
        self.input_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
    def start(self):
        """Start monitoring for file changes"""
        logger.info(f"Starting file monitor on: {self.input_dir}")
        
        self.handler = CSVFileHandler(self.input_dir, self.output_dir, self.callback)
        self.observer = Observer()
        self.observer.schedule(self.handler, str(self.input_dir), recursive=False)
        self.observer.start()
        
        logger.info("File monitor started successfully")
        
    def stop(self):
        """Stop monitoring"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            logger.info("File monitor stopped")
    
    def process_existing_files(self):
        """Process any existing CSV files in the input directory"""
        logger.info("Checking for existing CSV files...")
        
        csv_files = list(self.input_dir.glob("*.csv"))
        if csv_files:
            logger.info(f"Found {len(csv_files)} existing CSV files")
            for csv_file in csv_files:
                # Check if corresponding JSON already exists
                json_file = self.output_dir / f"{csv_file.stem}.json"
                if not json_file.exists():
                    logger.info(f"Processing existing file: {csv_file}")
                    self.handler.process_file(csv_file)
                else:
                    logger.info(f"JSON already exists for: {csv_file}")
        else:
            logger.info("No existing CSV files found")

def main():
    """Main function for standalone execution"""
    print("🔄 Starting CSV File Monitor...")
    print("📁 Watching for new CSV files in 'input_csvs' directory")
    print("📄 JSON files will be generated in 'output_jsons' directory")
    print("Press Ctrl+C to stop")
    
    monitor = FileMonitor()
    
    try:
        # Process any existing files first
        monitor.process_existing_files()
        
        # Start monitoring
        monitor.start()
        
        # Keep the script running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping file monitor...")
        monitor.stop()
        print("✅ File monitor stopped")

if __name__ == "__main__":
    main()
