# 🔄 Automated CSV to JSON System

An intelligent, automated system that converts CSV conversation logs to JSON format with automatic spreadsheet integration and web interface for annotators.

## ✨ Features

- 🔍 **Automatic File Monitoring** - Watches for new CSV files and processes them instantly
- 🌐 **Web Interface** - Easy drag-and-drop file upload for annotators
- 📊 **Spreadsheet Integration** - Automatically updates tracking spreadsheet with file links
- 🔗 **Flexible Link Generation** - Supports local paths, web URLs, and cloud storage links
- ⚡ **Real-time Processing** - Files are converted as soon as they're uploaded
- 📱 **Multi-platform Support** - Works on Windows, Linux, and macOS

## 🚀 Quick Start

### 1. Installation

Run the automated installer:

```bash
python install.py
```

This will:
- Install all dependencies
- Create necessary directories
- Generate configuration files
- Create startup scripts
- Test the installation

### 2. Start the System

**Windows:**
```bash
start_system.bat
```

**Linux/Mac:**
```bash
./start_system.sh
```

**Manual:**
```bash
python automated_csv_json_system.py
```

### 3. Access the Web Interface

Open your browser and go to: `http://localhost:5000`

## 📁 Project Structure

### Core Files
- `automated_csv_json_system.py` - Main system coordinator
- `csv_to_json.py` - Original conversion logic
- `file_monitor.py` - File watching and automatic processing
- `spreadsheet_integration.py` - Spreadsheet management
- `link_generator.py` - File link generation
- `web_interface.py` - Web upload interface

### Configuration Files
- `system_config.json` - Main system configuration
- `link_config_*.json` - Link generation configurations
- `requirements.txt` - Python dependencies

### Utility Files
- `install.py` - Automated installation script
- `setup.py` - Package setup configuration
- `start_system.*` - Platform-specific startup scripts

## 📋 How It Works

### For Annotators

1. **Upload CSV Files**
   - Use the web interface at `http://localhost:5000`
   - Drag and drop CSV files or click to browse
   - Enter your name and select a category
   - Click "Upload and Convert"

2. **Alternative: Direct File Copy**
   - Copy CSV files directly to the `input_csvs` folder
   - The system will automatically detect and process them

3. **Get Results**
   - JSON files appear in the `output_jsons` folder
   - Spreadsheet is automatically updated with file links
   - Download links are available in the web interface

### For Administrators

1. **Monitor the System**
   - Check `automated_system.log` for system activity
   - View `tracking_spreadsheet.csv` for all processed files
   - Access web interface for real-time status

2. **Configure the System**
   - Edit `system_config.json` for system settings
   - Choose appropriate link configuration
   - Customize categories and auto-categorization rules

## 📊 Spreadsheet Integration

The system automatically maintains a tracking spreadsheet with these columns:

- **Date** - When the file was processed
- **ID** - Unique identifier for the file
- **Annotator** - Name of the person who uploaded the file
- **Link to CSV** - Direct link to the original CSV file
- **Link to JSON** - Direct link to the generated JSON file
- **Category** - File category (conversation_logs, tool_trajectories, etc.)

## 🔗 Link Generation Options

### Local Development (Default)
```json
{
  "link_type": "relative",
  "base_url": "",
  "cloud_config": {}
}
```

### Web Server Hosting
```json
{
  "link_type": "web",
  "base_url": "https://your-domain.com/files",
  "cloud_config": {}
}
```

### Cloud Storage (AWS S3)
```json
{
  "link_type": "cloud",
  "base_url": "",
  "cloud_config": {
    "type": "aws",
    "bucket": "your-csv-json-bucket",
    "region": "us-east-1",
    "folder": "trajectories"
  }
}
```

## 📝 Input Format

The system expects CSV files with conversation log format containing these columns:
- `session_id` - Conversation session identifier
- `turn_id` - Turn identifier within session
- `event_type` - Type of event (user_message, tool_call_interrupt, tool_execution_approved, etc.)
- `role` - Message role (user, assistant, tool, system)
- `content` - Message content
- `tool_name` - Name of tool being called (for tool events)
- `original_args` - Tool arguments in JSON format
- `execution_result` - Tool execution results in JSON format

## 📄 Output Format

JSON files are generated in the `output_jsons/` directory with this structure:
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful AI assistant specialized in synthesizing information..."
    },
    {
      "role": "user",
      "content": "User question here"
    },
    {
      "role": "assistant",
      "tool_call": {
        "name": "tool_name",
        "arguments": {
          "__arg1": "argument_value"
        }
      }
    },
    {
      "role": "tool",
      "name": "tool_name",
      "content": "Tool response in JSON format"
    },
    {
      "role": "assistant",
      "content": "Final synthesized response"
    }
  ]
}
```

## ⚙️ Configuration

### System Configuration (`system_config.json`)

```json
{
  "input_dir": "input_csvs",
  "output_dir": "output_jsons",
  "spreadsheet_path": "tracking_spreadsheet.csv",
  "web_interface": {
    "enabled": true,
    "host": "0.0.0.0",
    "port": 5000
  },
  "link_generation": {
    "link_type": "relative",
    "base_url": "",
    "cloud_config": {}
  },
  "monitoring": {
    "process_existing": true,
    "auto_categorize": true
  }
}
```

### Customization Options

- **Directories**: Change input/output folder locations
- **Web Interface**: Enable/disable, change host/port
- **Link Generation**: Configure how file links are generated
- **Monitoring**: Control file processing behavior
- **Auto-categorization**: Customize category assignment rules

## 🔧 Advanced Usage

### Command Line Options

```bash
# Start with custom configuration
python automated_csv_json_system.py --config my_config.json

# Create sample configuration
python automated_csv_json_system.py --create-config

# Run only file monitoring (no web interface)
python file_monitor.py

# Run only web interface
python web_interface.py
```

### API Endpoints

The web interface provides these API endpoints:

- `GET /` - Main upload interface
- `POST /upload` - File upload endpoint
- `GET /download/<filename>` - File download
- `GET /api/status` - System status (JSON)

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_csv_to_json.py
```

Tests include:
- ✅ Dependencies verification
- ✅ Input file validation
- ✅ Conversion accuracy
- ✅ Output format compliance
- ✅ Data integrity checks

## 🚨 Troubleshooting

### Common Issues

1. **Port 5000 already in use**
   - Change port in `system_config.json`
   - Or kill the process using port 5000

2. **Permission denied on file operations**
   - Check folder permissions
   - Run with appropriate user privileges

3. **Dependencies not found**
   - Re-run `python install.py`
   - Check Python environment

4. **Files not being processed**
   - Check `automated_system.log` for errors
   - Verify file format matches expected CSV structure

### Log Files

- `automated_system.log` - Main system log
- `file_monitor.log` - File monitoring events
- Check web interface console for upload issues

## 📈 Performance

- **Processing Speed**: ~1-2 seconds per CSV file
- **Concurrent Uploads**: Supports multiple simultaneous uploads
- **Memory Usage**: Minimal (processes files individually)
- **Storage**: Original CSV files are preserved

