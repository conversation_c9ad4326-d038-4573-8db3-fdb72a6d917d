# Automated CSV-to-JSON Spreadsheet Integration System

This project provides an automated system that integrates with your existing spreadsheet to convert CSV conversation logs into structured JSON format. When annotators add CSV file paths to your spreadsheet, the system automatically generates JSON files and updates the spreadsheet with the links.

## 🎯 **Key Features**

- **Spreadsheet Integration** - Works with your existing Excel or CSV tracking spreadsheets
- **Automatic Processing** - Monitors spreadsheet for new entries and processes them automatically
- **Flexible Column Mapping** - Adapts to any spreadsheet structure and column names
- **Real-time Monitoring** - Continuous monitoring with configurable check intervals
- **Error Handling** - Graceful handling of missing files and malformed data
- **Preserves Data** - Only processes new entries, existing data remains untouched

## 🚀 **Quick Start**

### 1. Install Dependencies
```bash
pip install pandas openpyxl watchdog
```

### 2. Configure for Your Spreadsheet
```bash
python configure_for_existing_spreadsheet.py
```

### 3. Start Monitoring
```bash
# Continuous monitoring
python custom_spreadsheet_monitor.py --continuous

# Process once
python custom_spreadsheet_monitor.py
```

## 📊 **Spreadsheet Requirements**

Your spreadsheet needs these **3 required columns** (any names work):

| Purpose | Example Names | Required |
|---------|---------------|----------|
| **Unique ID** | ID, Entry_ID, Record_ID | ✅ |
| **CSV File Path** | Link to CSV, CSV_Path, File_Location | ✅ |
| **JSON File Path** | Link to JSON, JSON_Path, Output_File | ✅ |

**Optional columns**: Date, Annotator, Category, Status, Notes

## 📋 **Workflow for Annotators**

1. **Open your spreadsheet** (Excel or CSV)
2. **Add new row** with CSV file path
3. **Leave JSON column empty**
4. **Save spreadsheet**

The system automatically:
- Detects new entries
- Converts CSV to JSON
- Updates JSON file links

## 📁 **File Structure**

```
CSV-to-JSON/
├── csv_to_json.py                          # Core conversion logic
├── configure_for_existing_spreadsheet.py   # Setup script
├── custom_spreadsheet_monitor.py           # Custom monitor
├── custom_monitor_config.json              # Configuration
├── requirements.txt                        # Dependencies
├── SETUP_GUIDE_FOR_YOUR_SPREADSHEET.md    # Detailed setup guide
├── input_csvs/                             # CSV files location
└── output_jsons/                           # Generated JSON files
```

## 🔧 **CSV Format**

Input CSV files should contain conversation log columns:
- `id`, `session_id`, `turn_id`, `timestamp`
- `event_type`, `role`, `content`
- `tool_name`, `original_args`, `execution_result`
- `model_used`, `extra`

## 📄 **JSON Output Format**

Structured conversation format:
```json
{
  "messages": [
    {
      "role": "system",
      "content": "System message..."
    },
    {
      "role": "user",
      "content": "User message..."
    },
    {
      "role": "assistant",
      "reasoning": ["reasoning steps"],
      "tool_call": {
        "name": "tool_name",
        "arguments": {...}
      }
    },
    {
      "role": "tool",
      "name": "tool_name",
      "content": "tool response"
    }
  ]
}
```

## 📖 **Documentation**

- **`SETUP_GUIDE_FOR_YOUR_SPREADSHEET.md`** - Complete setup instructions
- **Configuration script** - Interactive setup for any spreadsheet structure
- **Flexible monitoring** - Adapts to your workflow and column layout

## 🎉 **Benefits**

- ✅ **No manual work** - Fully automated conversion and link updates
- ✅ **Works with existing spreadsheets** - No need to change your current workflow
- ✅ **Flexible structure** - Adapts to any column layout
- ✅ **Real-time processing** - Immediate conversion when entries are added
- ✅ **Error resilient** - Handles missing files and data issues gracefully



