# CSV to JSON Conversation Converter

This project converts CSV conversation logs into clean JSON format.

## ✅ Status: FULLY WORKING

All dependencies are installed and the converter is working correctly.

## Files

- `csv_to_json.py` - Main conversion logic (updated for conversation format)
- `run_csv_to_json.py` - Simple script to run the converter
- `test_csv_to_json.py` - Comprehensive test suite
- `requirements.txt` - Python dependencies

## Dependencies

- **pandas** ✅ Installed and working
- **json** ✅ Built-in Python module
- **os** ✅ Built-in Python module
- **glob** ✅ Built-in Python module

## Usage

### Method 1: Direct execution
```bash
python csv_to_json.py
```

### Method 2: Using the run script
```bash
python run_csv_to_json.py
```

### Method 3: Import and use functions
```python
from csv_to_json import batch_transform
batch_transform("input_csvs", "output_jsons")
```

## Input Format

Place CSV files in the `input_csvs/` directory. The converter expects conversation log format with columns:
- `session_id` - Conversation session identifier
- `turn_id` - Turn identifier within session
- `event_type` - Type of event (user_message, tool_call_interrupt, tool_execution_approved, etc.)
- `role` - Message role (user, assistant, tool, system)
- `content` - Message content
- `tool_name` - Name of tool being called (for tool events)
- `original_args` - Tool arguments in JSON format
- `execution_result` - Tool execution results in JSON format

## Output Format

JSON files are generated in the `output_jsons/` directory with the structure specified:
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful AI assistant specialized in synthesizing information..."
    },
    {
      "role": "user",
      "content": "User question here"
    },
    {
      "role": "assistant",
      "tool_call": {
        "name": "tool_name",
        "arguments": {
          "__arg1": "argument_value"
        }
      }
    },
    {
      "role": "tool",
      "name": "tool_name",
      "content": "Tool response in JSON format"
    },
    {
      "role": "assistant",
      "content": "Final synthesized response"
    }
  ]
}
```

## Features

✅ **Conversation Structure**: Properly sequences system → user → assistant tool calls → tool responses → assistant final response → next user question
✅ **Turn-Based Organization**: Each user question is followed by all its tool interactions and a final synthesized response
✅ **Tool Call Format**: Matches the exact reference format with `tool_call` object and proper argument formatting
✅ **Tool Response Format**: Includes `name` field and properly formatted `content`
✅ **Reasoning Integration**: Includes reasoning in the first assistant tool call message per turn when available
✅ **System Message**: Automatically adds the standard system message with tool definitions
✅ **Multi-turn Support**: Handles multiple conversations and turns within sessions
✅ **Actual AI Responses**: Uses real AI responses from conversation logs (not generic placeholders)
✅ **Error Handling**: Gracefully handles malformed JSON and missing fields

## Test Results

✅ **All 5 tests passed:**
1. Dependencies - All required modules available
2. Input Files - CSV files readable and valid
3. Conversion - Batch processing works correctly
4. Output Validity - Generated JSON matches exact reference format
5. Data Integrity - Conversation flow properly maintained

## Current Status

- 📁 **Input**: 2 CSV files (original + sample)
- 📁 **Output**: 2 JSON files with proper conversation structure
- ✅ **Format compliance**: Matches your exact reference format
- ✅ **JSON validity**: All output files are valid JSON
- ✅ **Conversation flow**: Proper sequence of system → user → assistant → tool → assistant

