#!/usr/bin/env python3
"""
Spreadsheet-Integrated CSV-to-JSON System
Monitors spreadsheet for new CSV entries and automatically generates JSON files
"""

import os
import sys
import time
import pandas as pd
from pathlib import Path
import logging
from datetime import datetime
import json
from urllib.parse import urlparse
import requests
from csv_to_json import transform_csv_to_json

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('spreadsheet_integration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SpreadsheetIntegratedProcessor:
    """
    Monitors spreadsheet for new CSV entries and automatically processes them
    """
    
    def __init__(self, spreadsheet_path, config=None):
        """
        Initialize the processor
        
        Args:
            spreadsheet_path: Path to the tracking spreadsheet
            config: Configuration dictionary
        """
        self.spreadsheet_path = Path(spreadsheet_path)
        self.config = config or self._get_default_config()
        
        # Directories
        self.input_dir = Path(self.config.get('input_dir', 'input_csvs'))
        self.output_dir = Path(self.config.get('output_dir', 'output_jsons'))
        
        # Create directories
        self.input_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        # Tracking
        self.processed_entries = set()
        self.last_check_time = datetime.now()
        
    def _get_default_config(self):
        """Get default configuration"""
        return {
            'input_dir': 'input_csvs',
            'output_dir': 'output_jsons',
            'check_interval': 10,  # seconds
            'csv_column': 'Link to CSV',
            'json_column': 'Link to JSON',
            'date_column': 'Date',
            'id_column': 'ID',
            'annotator_column': 'Annotator',
            'category_column': 'Category',
            'auto_download': True,
            'base_url': ''  # Base URL for downloading CSV files if they're web links
        }
    
    def load_spreadsheet(self):
        """Load the spreadsheet"""
        try:
            if not self.spreadsheet_path.exists():
                logger.warning(f"Spreadsheet not found: {self.spreadsheet_path}")
                return None
                
            # Try different formats
            if self.spreadsheet_path.suffix.lower() == '.csv':
                df = pd.read_csv(self.spreadsheet_path)
            elif self.spreadsheet_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(self.spreadsheet_path)
            else:
                logger.error(f"Unsupported file format: {self.spreadsheet_path}")
                return None
                
            logger.info(f"Loaded spreadsheet with {len(df)} rows")
            return df
            
        except Exception as e:
            logger.error(f"Error loading spreadsheet: {e}")
            return None
    
    def save_spreadsheet(self, df):
        """Save the spreadsheet"""
        try:
            if self.spreadsheet_path.suffix.lower() == '.csv':
                df.to_csv(self.spreadsheet_path, index=False)
            elif self.spreadsheet_path.suffix.lower() in ['.xlsx', '.xls']:
                df.to_excel(self.spreadsheet_path, index=False)
            
            logger.info(f"Saved spreadsheet: {self.spreadsheet_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving spreadsheet: {e}")
            return False
    
    def download_csv_file(self, csv_link, local_filename):
        """Download CSV file from URL or copy from local path"""
        try:
            # Check if it's a URL
            if csv_link.startswith(('http://', 'https://')):
                logger.info(f"Downloading CSV from URL: {csv_link}")
                response = requests.get(csv_link)
                response.raise_for_status()
                
                with open(local_filename, 'wb') as f:
                    f.write(response.content)
                    
            elif csv_link.startswith(('file://', '/')):
                # Local file path
                source_path = Path(csv_link.replace('file://', ''))
                if source_path.exists():
                    import shutil
                    shutil.copy2(source_path, local_filename)
                    logger.info(f"Copied CSV file: {source_path} -> {local_filename}")
                else:
                    logger.error(f"Source CSV file not found: {source_path}")
                    return False
                    
            else:
                # Relative path
                source_path = Path(csv_link)
                if source_path.exists():
                    import shutil
                    shutil.copy2(source_path, local_filename)
                    logger.info(f"Copied CSV file: {source_path} -> {local_filename}")
                else:
                    logger.error(f"Source CSV file not found: {source_path}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error downloading/copying CSV file: {e}")
            return False
    
    def generate_json_link(self, csv_filename):
        """Generate JSON file link based on CSV filename"""
        json_filename = csv_filename.replace('.csv', '.json')
        
        # If base_url is configured, create web link
        if self.config.get('base_url'):
            base_url = self.config['base_url'].rstrip('/')
            return f"{base_url}/output_jsons/{json_filename}"
        else:
            # Return relative path
            return f"output_jsons/{json_filename}"
    
    def process_new_entries(self):
        """Check spreadsheet for new entries and process them"""
        df = self.load_spreadsheet()
        if df is None:
            return False
        
        csv_col = self.config['csv_column']
        json_col = self.config['json_column']
        id_col = self.config['id_column']
        
        # Check required columns exist
        required_cols = [csv_col, id_col]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"Missing required columns: {missing_cols}")
            return False
        
        # Add JSON column if it doesn't exist
        if json_col not in df.columns:
            df[json_col] = ''
        
        new_entries_processed = 0
        updated_rows = []
        
        for index, row in df.iterrows():
            entry_id = str(row[id_col])
            csv_link = str(row[csv_col]) if pd.notna(row[csv_col]) else ''
            json_link = str(row[json_col]) if pd.notna(row[json_col]) else ''
            
            # Skip if no CSV link or already processed
            if not csv_link or csv_link == 'nan' or entry_id in self.processed_entries:
                continue
            
            # Skip if JSON link already exists and is not empty
            if json_link and json_link != 'nan' and json_link.strip():
                self.processed_entries.add(entry_id)
                continue
            
            logger.info(f"Processing new entry: {entry_id}")
            
            try:
                # Generate local filenames
                csv_filename = f"{entry_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                local_csv_path = self.input_dir / csv_filename
                
                # Download/copy CSV file
                if self.download_csv_file(csv_link, local_csv_path):
                    # Convert to JSON
                    transform_csv_to_json(str(local_csv_path), str(self.output_dir))
                    
                    # Generate JSON link
                    json_filename = csv_filename.replace('.csv', '.json')
                    json_file_path = self.output_dir / json_filename
                    
                    if json_file_path.exists():
                        json_link = self.generate_json_link(json_filename)
                        
                        # Update the dataframe
                        df.at[index, json_col] = json_link
                        updated_rows.append(index)
                        
                        # Mark as processed
                        self.processed_entries.add(entry_id)
                        new_entries_processed += 1
                        
                        logger.info(f"✅ Processed: {entry_id} -> {json_link}")
                    else:
                        logger.error(f"JSON file not created for: {entry_id}")
                else:
                    logger.error(f"Failed to download CSV for: {entry_id}")
                    
            except Exception as e:
                logger.error(f"Error processing entry {entry_id}: {e}")
        
        # Save updated spreadsheet if there were changes
        if updated_rows:
            if self.save_spreadsheet(df):
                logger.info(f"📊 Updated spreadsheet with {len(updated_rows)} new JSON links")
            else:
                logger.error("Failed to save updated spreadsheet")
        
        if new_entries_processed > 0:
            logger.info(f"🎉 Processed {new_entries_processed} new entries")
        
        return new_entries_processed > 0
    
    def run_continuous_monitoring(self):
        """Run continuous monitoring of the spreadsheet"""
        logger.info("🔄 Starting continuous spreadsheet monitoring...")
        logger.info(f"📊 Monitoring: {self.spreadsheet_path}")
        logger.info(f"📁 Input directory: {self.input_dir}")
        logger.info(f"📄 Output directory: {self.output_dir}")
        logger.info(f"⏱️ Check interval: {self.config['check_interval']} seconds")
        
        try:
            while True:
                logger.info("🔍 Checking for new entries...")
                self.process_new_entries()
                
                logger.info(f"😴 Waiting {self.config['check_interval']} seconds...")
                time.sleep(self.config['check_interval'])
                
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring stopped by user")
        except Exception as e:
            logger.error(f"❌ Monitoring error: {e}")
    
    def process_once(self):
        """Process spreadsheet once and exit"""
        logger.info("🔄 Processing spreadsheet once...")
        result = self.process_new_entries()
        
        if result:
            logger.info("✅ Processing completed successfully")
        else:
            logger.info("ℹ️ No new entries to process")
        
        return result

def create_sample_spreadsheet():
    """Create a sample spreadsheet for testing"""
    sample_data = {
        'Date': [
            '2025-10-08 10:00:00',
            '2025-10-08 10:15:00',
            '2025-10-08 10:30:00'
        ],
        'ID': ['ENTRY001', 'ENTRY002', 'ENTRY003'],
        'Annotator': ['Alice', 'Bob', 'Charlie'],
        'Link to CSV': [
            'input_csvs/MSA024.csv',
            'input_csvs/MSA29.csv',
            'input_csvs/chat-session-logs-0c0eae76-2d8c-4f36-84b7-8630f2ea8435-2025-10-07.csv'
        ],
        'Link to JSON': ['', '', ''],  # Empty - will be filled automatically
        'Category': ['conversation_logs', 'tool_trajectories', 'user_interactions']
    }
    
    df = pd.DataFrame(sample_data)
    df.to_csv('sample_tracking_spreadsheet.csv', index=False)
    print("📊 Created sample_tracking_spreadsheet.csv")
    return 'sample_tracking_spreadsheet.csv'

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Spreadsheet-Integrated CSV-to-JSON Processor")
    parser.add_argument("--spreadsheet", default="tracking_spreadsheet.csv", 
                       help="Path to tracking spreadsheet")
    parser.add_argument("--continuous", action="store_true", 
                       help="Run continuous monitoring")
    parser.add_argument("--create-sample", action="store_true", 
                       help="Create sample spreadsheet")
    parser.add_argument("--interval", type=int, default=10, 
                       help="Check interval in seconds (for continuous mode)")
    
    args = parser.parse_args()
    
    if args.create_sample:
        create_sample_spreadsheet()
        return
    
    # Configuration
    config = {
        'check_interval': args.interval,
        'csv_column': 'Link to CSV',
        'json_column': 'Link to JSON',
        'date_column': 'Date',
        'id_column': 'ID',
        'annotator_column': 'Annotator',
        'category_column': 'Category',
        'auto_download': True,
        'base_url': ''  # Set this if you want web URLs
    }
    
    # Initialize processor
    processor = SpreadsheetIntegratedProcessor(args.spreadsheet, config)
    
    print("🔄 Spreadsheet-Integrated CSV-to-JSON System")
    print("=" * 50)
    print(f"📊 Spreadsheet: {args.spreadsheet}")
    print(f"📁 Input directory: {processor.input_dir}")
    print(f"📄 Output directory: {processor.output_dir}")
    
    if args.continuous:
        print("🔄 Running in continuous monitoring mode...")
        print("Press Ctrl+C to stop")
        processor.run_continuous_monitoring()
    else:
        print("🔄 Processing once...")
        processor.process_once()

if __name__ == "__main__":
    main()
