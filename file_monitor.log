2025-10-08 14:37:24,781 - INFO - Loaded configuration from system_config.json
2025-10-08 14:37:24,808 - INFO - Initializing automated CSV-JSON system...
2025-10-08 14:37:24,808 - INFO - Spreadsheet not found, creating new one: tracking_spreadsheet.csv
2025-10-08 14:37:24,811 - INFO - Saved spreadsheet: tracking_spreadsheet.csv
2025-10-08 14:37:24,811 - INFO - Created new spreadsheet with required columns
2025-10-08 14:37:24,812 - INFO - System initialization completed successfully
2025-10-08 14:37:24,812 - INFO - Starting file monitoring...
2025-10-08 14:37:24,812 - INFO - Checking for existing CSV files...
2025-10-08 14:37:24,813 - INFO - Found 5 existing CSV files
2025-10-08 14:37:24,813 - INFO - JSON already exists for: input_csvs\chat-session-logs-0c0eae76-2d8c-4f36-84b7-8630f2ea8435-2025-10-07.csv
2025-10-08 14:37:24,813 - INFO - JSON already exists for: input_csvs\chat-session-logs-74c62132-0526-4de7-9842-53c687452daa-2025-10-07.csv
2025-10-08 14:37:24,813 - INFO - JSON already exists for: input_csvs\chat-session-logs-80d3d8ba-c151-491c-9bf1-42cf28951201-2025-10-07.csv
2025-10-08 14:37:24,813 - INFO - JSON already exists for: input_csvs\MSA024.csv
2025-10-08 14:37:24,813 - INFO - JSON already exists for: input_csvs\MSA29.csv
2025-10-08 14:37:24,814 - INFO - Starting file monitor on: input_csvs
2025-10-08 14:37:24,815 - INFO - File monitor started successfully
2025-10-08 14:37:24,815 - INFO - File monitoring started successfully
2025-10-08 14:37:24,815 - INFO - Starting web interface...
2025-10-08 14:37:24,815 - INFO - Web interface started on http://0.0.0.0:5000
2025-10-08 14:37:24,834 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-10-08 14:37:24,834 - INFO - [33mPress CTRL+C to quit[0m
2025-10-08 14:38:07,300 - INFO - 127.0.0.1 - - [08/Oct/2025 14:38:07] "GET / HTTP/1.1" 200 -
2025-10-08 14:38:07,463 - INFO - 127.0.0.1 - - [08/Oct/2025 14:38:07] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-10-08 14:39:39,685 - INFO - 127.0.0.1 - - [08/Oct/2025 14:39:39] "[31m[1mPOST / HTTP/1.1[0m" 405 -
2025-10-08 14:40:16,857 - INFO - 127.0.0.1 - - [08/Oct/2025 14:40:16] "[31m[1mPOST / HTTP/1.1[0m" 405 -
2025-10-08 14:40:24,972 - INFO - 127.0.0.1 - - [08/Oct/2025 14:40:24] "GET /download/chat-session-logs-80d3d8ba-c151-491c-9bf1-42cf28951201-2025-10-07.json HTTP/1.1" 200 -
2025-10-08 14:53:21,825 - INFO - New CSV file detected: input_csvs\test_new_entry.csv
2025-10-08 14:53:24,838 - INFO - Processing CSV file: input_csvs\test_new_entry.csv
2025-10-08 14:53:24,840 - ERROR - Error processing input_csvs\test_new_entry.csv: Error tokenizing data. C error: Expected 14 fields in line 4, saw 15

2025-10-08 14:55:18,543 - INFO - New CSV file detected: input_csvs\live_test_entry.csv
2025-10-08 14:55:21,545 - INFO - Processing CSV file: input_csvs\live_test_entry.csv
2025-10-08 14:55:21,552 - INFO - Processing callback for live_test_entry.csv
2025-10-08 14:55:21,554 - ERROR - Error adding file entry: 'input_csvs\\live_test_entry.csv' is not in the subpath of 'C:\\Users\\<USER>\\Desktop\\CSV-to-JSON'
2025-10-08 14:55:21,554 - ERROR - Failed to update spreadsheet for live_test_entry.csv
2025-10-08 14:55:21,555 - INFO - Successfully processed: input_csvs\live_test_entry.csv
