#!/usr/bin/env python3
"""
Comprehensive test suite for the Automated CSV-JSON System
"""

import os
import sys
import json
import time
import shutil
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock
import pandas as pd

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

# Import modules to test
try:
    from csv_to_json import transform_csv_to_json, batch_transform
    from file_monitor import FileMonitor, CSVFileHandler
    from spreadsheet_integration import SpreadsheetManager, AutomatedSpreadsheetUpdater
    from link_generator import LinkGenerator, LinkConfiguration
    from automated_csv_json_system import AutomatedCSVJSONSystem
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure all modules are in the current directory")
    sys.exit(1)

class TestCSVToJSON(unittest.TestCase):
    """Test the core CSV to JSON conversion functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.input_dir = self.test_dir / "input"
        self.output_dir = self.test_dir / "output"
        self.input_dir.mkdir()
        self.output_dir.mkdir()
        
        # Create sample CSV data
        self.sample_csv_data = {
            'id': [1, 2, 3, 4],
            'session_id': ['sess1', 'sess1', 'sess1', 'sess1'],
            'turn_id': ['turn1', 'turn1', 'turn1', 'turn1'],
            'timestamp': ['2025-10-07T12:00:00Z', '2025-10-07T12:00:01Z', 
                         '2025-10-07T12:00:02Z', '2025-10-07T12:00:03Z'],
            'event_type': ['user_message', 'tool_call_interrupt', 'tool_execution_approved', 'ai_response'],
            'role': ['user', 'assistant', 'tool', 'assistant'],
            'content': ['Test question', '', 'Tool result', 'Final answer'],
            'tool_name': ['', 'test_tool', 'test_tool', ''],
            'original_args': ['', '{"query": "test"}', '', ''],
            'execution_result': ['', '', '{"result": "success"}', '']
        }
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_csv_to_json_conversion(self):
        """Test basic CSV to JSON conversion"""
        # Create test CSV file
        csv_file = self.input_dir / "test.csv"
        df = pd.DataFrame(self.sample_csv_data)
        df.to_csv(csv_file, index=False)
        
        # Convert to JSON
        transform_csv_to_json(str(csv_file), str(self.output_dir))
        
        # Check if JSON file was created
        json_file = self.output_dir / "test.json"
        self.assertTrue(json_file.exists(), "JSON file should be created")
        
        # Validate JSON content
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        self.assertIn('messages', data, "JSON should contain 'messages' key")
        self.assertIsInstance(data['messages'], list, "Messages should be a list")
        self.assertGreater(len(data['messages']), 0, "Should have at least one message")
    
    def test_batch_conversion(self):
        """Test batch conversion of multiple files"""
        # Create multiple test CSV files
        for i in range(3):
            csv_file = self.input_dir / f"test_{i}.csv"
            df = pd.DataFrame(self.sample_csv_data)
            df.to_csv(csv_file, index=False)
        
        # Run batch conversion
        batch_transform(str(self.input_dir), str(self.output_dir))
        
        # Check if all JSON files were created
        json_files = list(self.output_dir.glob("*.json"))
        self.assertEqual(len(json_files), 3, "Should create 3 JSON files")

class TestFileMonitor(unittest.TestCase):
    """Test the file monitoring functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.input_dir = self.test_dir / "input"
        self.output_dir = self.test_dir / "output"
        self.input_dir.mkdir()
        self.output_dir.mkdir()
        
        self.callback_called = False
        self.callback_files = []
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def callback_function(self, csv_file, json_file):
        """Test callback function"""
        self.callback_called = True
        self.callback_files.append((csv_file, json_file))
    
    def test_file_monitor_initialization(self):
        """Test file monitor initialization"""
        monitor = FileMonitor(
            input_dir=str(self.input_dir),
            output_dir=str(self.output_dir),
            callback=self.callback_function
        )
        
        self.assertIsNotNone(monitor, "Monitor should be created")
        self.assertEqual(str(monitor.input_dir), str(self.input_dir))
        self.assertEqual(str(monitor.output_dir), str(self.output_dir))
    
    def test_csv_file_handler(self):
        """Test CSV file handler"""
        handler = CSVFileHandler(
            input_dir=str(self.input_dir),
            output_dir=str(self.output_dir),
            callback=self.callback_function
        )
        
        # Create a test CSV file
        csv_file = self.input_dir / "test.csv"
        df = pd.DataFrame({
            'session_id': ['test'],
            'turn_id': ['turn1'],
            'event_type': ['user_message'],
            'role': ['user'],
            'content': ['test message'],
            'timestamp': ['2025-10-07T12:00:00Z']
        })
        df.to_csv(csv_file, index=False)
        
        # Process the file
        handler.process_file(csv_file)
        
        # Check if callback was called
        self.assertTrue(self.callback_called, "Callback should be called")
        self.assertEqual(len(self.callback_files), 1, "Should process one file")

class TestSpreadsheetIntegration(unittest.TestCase):
    """Test spreadsheet integration functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.spreadsheet_path = self.test_dir / "test_spreadsheet.csv"
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_spreadsheet_manager_creation(self):
        """Test spreadsheet manager creation"""
        manager = SpreadsheetManager(str(self.spreadsheet_path))
        self.assertTrue(manager.load_spreadsheet(), "Should load/create spreadsheet")
        self.assertTrue(self.spreadsheet_path.exists(), "Spreadsheet file should exist")
    
    def test_add_file_entry(self):
        """Test adding file entries to spreadsheet"""
        manager = SpreadsheetManager(str(self.spreadsheet_path))
        manager.load_spreadsheet()
        
        csv_file = Path("test.csv")
        json_file = Path("test.json")
        
        success = manager.add_file_entry(csv_file, json_file, "TestAnnotator", "test_category")
        self.assertTrue(success, "Should successfully add entry")
        
        # Check if entry was added
        self.assertEqual(len(manager.df), 1, "Should have one entry")
        self.assertEqual(manager.df.iloc[0]['Annotator'], "TestAnnotator")
    
    def test_automated_updater(self):
        """Test automated spreadsheet updater"""
        updater = AutomatedSpreadsheetUpdater(str(self.spreadsheet_path))
        self.assertTrue(updater.initialize(), "Should initialize successfully")
        
        csv_file = Path("test.csv")
        json_file = Path("test.json")
        
        success = updater.process_new_files(csv_file, json_file, "TestUser", "test")
        self.assertTrue(success, "Should process files successfully")

class TestLinkGenerator(unittest.TestCase):
    """Test link generation functionality"""
    
    def test_relative_links(self):
        """Test relative link generation"""
        config = {"link_type": "relative"}
        generator = LinkGenerator(config)
        
        csv_file = Path("input/test.csv")
        link = generator.generate_csv_link(csv_file)
        
        self.assertIsInstance(link, str, "Link should be a string")
        self.assertIn("test.csv", link, "Link should contain filename")
    
    def test_web_links(self):
        """Test web link generation"""
        config = {
            "link_type": "web",
            "base_url": "https://example.com/files"
        }
        generator = LinkGenerator(config)
        
        csv_file = Path("test.csv")
        link = generator.generate_csv_link(csv_file)
        
        self.assertTrue(link.startswith("https://"), "Should generate web URL")
        self.assertIn("example.com", link, "Should contain base URL")
    
    def test_link_configuration(self):
        """Test link configuration management"""
        config_file = Path(tempfile.mktemp(suffix=".json"))
        
        try:
            link_config = LinkConfiguration(str(config_file))
            generator = link_config.get_generator()
            
            self.assertIsNotNone(generator, "Should create generator")
            
            # Test configuration update
            success = link_config.update_config(link_type="web", base_url="https://test.com")
            self.assertTrue(success, "Should update configuration")
            
        finally:
            if config_file.exists():
                config_file.unlink()

class TestAutomatedSystem(unittest.TestCase):
    """Test the complete automated system"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.config_file = self.test_dir / "test_config.json"
        
        # Create test configuration
        self.test_config = {
            "input_dir": str(self.test_dir / "input"),
            "output_dir": str(self.test_dir / "output"),
            "spreadsheet_path": str(self.test_dir / "spreadsheet.csv"),
            "web_interface": {"enabled": False},
            "link_generation": {"link_type": "relative"},
            "monitoring": {"process_existing": True, "auto_categorize": True}
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(self.test_config, f)
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_system_initialization(self):
        """Test system initialization"""
        system = AutomatedCSVJSONSystem(str(self.config_file))
        self.assertTrue(system.initialize(), "System should initialize successfully")
        
        # Check if directories were created
        input_dir = Path(self.test_config["input_dir"])
        output_dir = Path(self.test_config["output_dir"])
        
        self.assertTrue(input_dir.exists(), "Input directory should be created")
        self.assertTrue(output_dir.exists(), "Output directory should be created")
    
    def test_config_loading(self):
        """Test configuration loading"""
        system = AutomatedCSVJSONSystem(str(self.config_file))
        
        self.assertEqual(system.config["input_dir"], self.test_config["input_dir"])
        self.assertEqual(system.config["output_dir"], self.test_config["output_dir"])
    
    @patch('automated_csv_json_system.app.run')
    def test_system_start_without_web(self, mock_app_run):
        """Test system start without web interface"""
        # Disable web interface
        self.test_config["web_interface"]["enabled"] = False
        with open(self.config_file, 'w') as f:
            json.dump(self.test_config, f)
        
        system = AutomatedCSVJSONSystem(str(self.config_file))
        
        # Mock the file monitor to avoid actual file watching
        with patch.object(system, 'file_monitor') as mock_monitor:
            mock_monitor.start.return_value = True
            mock_monitor.process_existing_files.return_value = None
            
            success = system.start()
            self.assertTrue(success, "System should start successfully")
            
            # Web interface should not be called
            mock_app_run.assert_not_called()

def run_all_tests():
    """Run all tests and return results"""
    print("🧪 Running Automated CSV-JSON System Test Suite")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestCSVToJSON,
        TestFileMonitor,
        TestSpreadsheetIntegration,
        TestLinkGenerator,
        TestAutomatedSystem
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    if not result.failures and not result.errors:
        print("✅ All tests passed!")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
