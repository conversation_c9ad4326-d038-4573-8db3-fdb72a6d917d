#!/usr/bin/env python3
"""
Setup script for the Automated CSV-JSON System
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text()

setup(
    name="automated-csv-json-system",
    version="1.0.0",
    description="Automated system for converting CSV conversation logs to JSON format with spreadsheet integration",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Your Name",
    author_email="<EMAIL>",
    url="https://github.com/yourusername/csv-to-json",
    packages=find_packages(),
    install_requires=[
        "pandas>=1.3.0",
        "watchdog>=2.1.0",
        "flask>=2.0.0",
        "openpyxl>=3.0.0",
        "werkzeug>=2.0.0"
    ],
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.0.0",
            "black>=21.0.0",
            "flake8>=3.8.0"
        ],
        "cloud": [
            "boto3>=1.20.0",  # For AWS S3
            "google-cloud-storage>=2.0.0",  # For Google Cloud Storage
            "azure-storage-blob>=12.0.0"  # For Azure Blob Storage
        ]
    },
    python_requires=">=3.7",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    entry_points={
        "console_scripts": [
            "csv-json-system=automated_csv_json_system:main",
            "csv-json-monitor=file_monitor:main",
            "csv-json-web=web_interface:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
