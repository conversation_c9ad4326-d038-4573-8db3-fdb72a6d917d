#!/usr/bin/env python3
"""
Simple Spreadsheet Monitor for CSV-to-JSON Conversion
Watches your tracking spreadsheet and automatically converts CSV files to JSON
"""

import pandas as pd
import time
import os
from pathlib import Path
from csv_to_json import transform_csv_to_json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleSpreadsheetMonitor:
    """
    Simple monitor that watches your spreadsheet for new CSV entries
    and automatically generates JSON files
    """
    
    def __init__(self, spreadsheet_path="tracking_spreadsheet.csv"):
        self.spreadsheet_path = Path(spreadsheet_path)
        self.processed_ids = set()
        
        # Create output directory
        self.output_dir = Path("output_jsons")
        self.output_dir.mkdir(exist_ok=True)
        
    def check_and_process(self):
        """Check spreadsheet for new entries and process them"""
        try:
            # Load spreadsheet
            if not self.spreadsheet_path.exists():
                logger.warning(f"Spreadsheet not found: {self.spreadsheet_path}")
                return
            
            # Read the spreadsheet
            if self.spreadsheet_path.suffix.lower() == '.csv':
                df = pd.read_csv(self.spreadsheet_path)
            else:
                df = pd.read_excel(self.spreadsheet_path)
            
            # Check if required columns exist
            required_columns = ['ID', 'Link to CSV', 'Link to JSON']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"Missing columns in spreadsheet: {missing_columns}")
                return
            
            # Process each row
            updated = False
            for index, row in df.iterrows():
                entry_id = str(row['ID'])
                csv_link = str(row['Link to CSV']) if pd.notna(row['Link to CSV']) else ''
                json_link = str(row['Link to JSON']) if pd.notna(row['Link to JSON']) else ''
                
                # Skip if already processed or no CSV link
                if entry_id in self.processed_ids or not csv_link or csv_link == 'nan':
                    continue
                
                # Skip if JSON link already exists
                if json_link and json_link != 'nan' and json_link.strip():
                    self.processed_ids.add(entry_id)
                    continue
                
                # Process this entry
                logger.info(f"🔄 Processing entry: {entry_id}")
                
                # Get CSV file path
                csv_path = Path(csv_link)
                
                # Check if CSV file exists
                if not csv_path.exists():
                    logger.warning(f"CSV file not found: {csv_path}")
                    continue
                
                try:
                    # Convert CSV to JSON
                    transform_csv_to_json(str(csv_path), str(self.output_dir))
                    
                    # Generate JSON file path
                    json_filename = csv_path.stem + '.json'
                    json_path = self.output_dir / json_filename
                    
                    if json_path.exists():
                        # Update the spreadsheet with JSON link
                        json_link = f"output_jsons/{json_filename}"
                        df.at[index, 'Link to JSON'] = json_link
                        
                        logger.info(f"✅ Created JSON: {json_link}")
                        updated = True
                        self.processed_ids.add(entry_id)
                    else:
                        logger.error(f"❌ JSON file not created for: {entry_id}")
                        
                except Exception as e:
                    logger.error(f"❌ Error processing {entry_id}: {e}")
            
            # Save updated spreadsheet
            if updated:
                if self.spreadsheet_path.suffix.lower() == '.csv':
                    df.to_csv(self.spreadsheet_path, index=False)
                else:
                    df.to_excel(self.spreadsheet_path, index=False)
                
                logger.info("📊 Spreadsheet updated with new JSON links")
                
        except Exception as e:
            logger.error(f"❌ Error checking spreadsheet: {e}")
    
    def run_continuous(self, interval=10):
        """Run continuous monitoring"""
        logger.info("🔄 Starting continuous monitoring...")
        logger.info(f"📊 Watching: {self.spreadsheet_path}")
        logger.info(f"📁 JSON output: {self.output_dir}")
        logger.info(f"⏱️ Check interval: {interval} seconds")
        logger.info("Press Ctrl+C to stop")
        
        try:
            while True:
                self.check_and_process()
                time.sleep(interval)
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring stopped")
    
    def run_once(self):
        """Run once and exit"""
        logger.info("🔄 Processing spreadsheet once...")
        self.check_and_process()
        logger.info("✅ Processing complete")

def create_example_spreadsheet():
    """Create an example spreadsheet"""
    data = {
        'Date': ['2025-10-08 10:00:00', '2025-10-08 10:15:00', '2025-10-08 10:30:00'],
        'ID': ['ENTRY001', 'ENTRY002', 'ENTRY003'],
        'Annotator': ['Alice', 'Bob', 'Charlie'],
        'Link to CSV': [
            'input_csvs/MSA024.csv',
            'input_csvs/MSA29.csv', 
            'input_csvs/chat-session-logs-0c0eae76-2d8c-4f36-84b7-8630f2ea8435-2025-10-07.csv'
        ],
        'Link to JSON': ['', '', ''],  # Will be filled automatically
        'Category': ['conversation_logs', 'tool_trajectories', 'user_interactions']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('example_tracking_spreadsheet.csv', index=False)
    print("📊 Created example_tracking_spreadsheet.csv")
    print("📝 Edit the 'Link to CSV' column with your CSV file paths")
    print("🔄 Then run the monitor to auto-generate JSON links")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Simple Spreadsheet Monitor for CSV-to-JSON")
    parser.add_argument("--spreadsheet", "-s", default="tracking_spreadsheet.csv",
                       help="Path to your tracking spreadsheet")
    parser.add_argument("--continuous", "-c", action="store_true",
                       help="Run continuous monitoring")
    parser.add_argument("--interval", "-i", type=int, default=10,
                       help="Check interval in seconds (default: 10)")
    parser.add_argument("--create-example", action="store_true",
                       help="Create an example spreadsheet")
    
    args = parser.parse_args()
    
    if args.create_example:
        create_example_spreadsheet()
        return
    
    # Initialize monitor
    monitor = SimpleSpreadsheetMonitor(args.spreadsheet)
    
    print("📊 Simple CSV-to-JSON Spreadsheet Monitor")
    print("=" * 45)
    print(f"📋 Spreadsheet: {args.spreadsheet}")
    print(f"📁 JSON output: output_jsons/")
    print()
    print("💡 How it works:")
    print("1. Add CSV file paths to 'Link to CSV' column")
    print("2. Leave 'Link to JSON' column empty")
    print("3. Run this monitor")
    print("4. JSON files are created and links are added automatically")
    print()
    
    if args.continuous:
        monitor.run_continuous(args.interval)
    else:
        monitor.run_once()

if __name__ == "__main__":
    main()
