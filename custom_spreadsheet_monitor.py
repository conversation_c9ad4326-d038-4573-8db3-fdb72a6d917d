#!/usr/bin/env python3
"""
Custom spreadsheet monitor for your existing spreadsheet
Auto-generated configuration script
"""

import pandas as pd
import time
import os
import json
from pathlib import Path
from csv_to_json import transform_csv_to_json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class CustomSpreadsheetMonitor:
    """Custom monitor for your existing spreadsheet"""
    
    def __init__(self, config_file="custom_monitor_config.json"):
        # Load configuration
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        self.spreadsheet_path = Path(self.config['spreadsheet_path'])
        self.column_mapping = self.config['column_mapping']
        self.processed_ids = set()
        
        # Create output directory
        self.output_dir = Path(self.config['output_dir'])
        self.output_dir.mkdir(exist_ok=True)
        
        # Required columns
        self.id_col = self.column_mapping['id_column']
        self.csv_col = self.column_mapping['csv_column']
        self.json_col = self.column_mapping['json_column']
        
        logger.info(f"Initialized monitor for: {self.spreadsheet_path}")
        logger.info(f"ID column: {self.id_col}")
        logger.info(f"CSV column: {self.csv_col}")
        logger.info(f"JSON column: {self.json_col}")
    
    def load_spreadsheet(self):
        """Load your spreadsheet"""
        try:
            if not self.spreadsheet_path.exists():
                logger.error(f"Spreadsheet not found: {self.spreadsheet_path}")
                return None
            
            if self.spreadsheet_path.suffix.lower() == '.csv':
                df = pd.read_csv(self.spreadsheet_path)
            else:
                df = pd.read_excel(self.spreadsheet_path)
            
            logger.info(f"Loaded spreadsheet with {len(df)} rows")
            return df
            
        except Exception as e:
            logger.error(f"Error loading spreadsheet: {e}")
            return None
    
    def save_spreadsheet(self, df):
        """Save your spreadsheet"""
        try:
            if self.spreadsheet_path.suffix.lower() == '.csv':
                df.to_csv(self.spreadsheet_path, index=False)
            else:
                df.to_excel(self.spreadsheet_path, index=False)
            
            logger.info(f"Saved spreadsheet: {self.spreadsheet_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving spreadsheet: {e}")
            return False
    
    def check_and_process(self):
        """Check spreadsheet for new entries and process them"""
        try:
            df = self.load_spreadsheet()
            if df is None:
                return
            
            # Check if required columns exist
            missing_cols = []
            for col in [self.id_col, self.csv_col]:
                if col not in df.columns:
                    missing_cols.append(col)
            
            if missing_cols:
                logger.error(f"Missing required columns: {missing_cols}")
                return
            
            # Add JSON column if it doesn't exist
            if self.json_col not in df.columns:
                df[self.json_col] = ''
                logger.info(f"Added missing JSON column: {self.json_col}")
            
            # Process each row
            updated = False
            for index, row in df.iterrows():
                entry_id = str(row[self.id_col])
                csv_link = str(row[self.csv_col]) if pd.notna(row[self.csv_col]) else ''
                json_link = str(row[self.json_col]) if pd.notna(row[self.json_col]) else ''
                
                # Skip if already processed or no CSV link
                if entry_id in self.processed_ids or not csv_link or csv_link == 'nan':
                    continue
                
                # Skip if JSON link already exists
                if json_link and json_link != 'nan' and json_link.strip():
                    self.processed_ids.add(entry_id)
                    continue
                
                # Process this entry
                logger.info(f"Processing entry: {entry_id}")
                
                # Get CSV file path
                csv_path = Path(csv_link)
                
                # Check if CSV file exists
                if not csv_path.exists():
                    logger.warning(f"CSV file not found: {csv_path}")
                    continue
                
                try:
                    # Convert CSV to JSON
                    transform_csv_to_json(str(csv_path), str(self.output_dir))
                    
                    # Generate JSON file path
                    json_filename = csv_path.stem + '.json'
                    json_path = self.output_dir / json_filename
                    
                    if json_path.exists():
                        # Update the spreadsheet with JSON link
                        json_link = f"{self.config['output_dir']}/{json_filename}"
                        df.at[index, self.json_col] = json_link
                        
                        logger.info(f"Created JSON: {json_link}")
                        updated = True
                        self.processed_ids.add(entry_id)
                    else:
                        logger.error(f"JSON file not created for: {entry_id}")
                        
                except Exception as e:
                    logger.error(f"Error processing {entry_id}: {e}")
            
            # Save updated spreadsheet
            if updated:
                self.save_spreadsheet(df)
                logger.info("Spreadsheet updated with new JSON links")
                
        except Exception as e:
            logger.error(f"Error checking spreadsheet: {e}")
    
    def run_continuous(self, interval=None):
        """Run continuous monitoring"""
        interval = interval or self.config['check_interval']
        
        logger.info("Starting continuous monitoring...")
        logger.info(f"Watching: {self.spreadsheet_path}")
        logger.info(f"JSON output: {self.output_dir}")
        logger.info(f"Check interval: {interval} seconds")
        logger.info("Press Ctrl+C to stop")
        
        try:
            while True:
                self.check_and_process()
                time.sleep(interval)
        except KeyboardInterrupt:
            logger.info("Monitoring stopped")
    
    def run_once(self):
        """Run once and exit"""
        logger.info("Processing spreadsheet once...")
        self.check_and_process()
        logger.info("Processing complete")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Custom Spreadsheet Monitor")
    parser.add_argument("--continuous", "-c", action="store_true", help="Run continuous monitoring")
    parser.add_argument("--interval", "-i", type=int, default=10, help="Check interval in seconds")
    
    args = parser.parse_args()
    
    monitor = CustomSpreadsheetMonitor()
    
    print("Custom Spreadsheet Monitor")
    print("=" * 35)
    print(f"Spreadsheet: {monitor.spreadsheet_path}")
    print(f"JSON output: {monitor.output_dir}")
    print()
    
    if args.continuous:
        monitor.run_continuous(args.interval)
    else:
        monitor.run_once()

if __name__ == "__main__":
    main()
