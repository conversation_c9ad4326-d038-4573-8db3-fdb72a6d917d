#!/usr/bin/env python3
"""
Installation and setup script for the Automated CSV-JSON System
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        # Install from requirements.txt
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        "input_csvs",
        "output_jsons",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   Created: {directory}")
    
    print("✅ Directories created")
    return True

def create_sample_config():
    """Create sample configuration files"""
    print("⚙️ Creating configuration files...")
    
    # System configuration
    system_config = {
        "input_dir": "input_csvs",
        "output_dir": "output_jsons",
        "spreadsheet_path": "tracking_spreadsheet.csv",
        "web_interface": {
            "enabled": True,
            "host": "0.0.0.0",
            "port": 5000
        },
        "link_generation": {
            "link_type": "relative",
            "base_url": "",
            "cloud_config": {}
        },
        "monitoring": {
            "process_existing": True,
            "auto_categorize": True
        }
    }
    
    with open("system_config.json", "w") as f:
        json.dump(system_config, f, indent=2)
    print("   Created: system_config.json")
    
    # Link configuration examples
    link_configs = {
        "local": {
            "link_type": "relative",
            "base_url": "",
            "cloud_config": {}
        },
        "web": {
            "link_type": "web",
            "base_url": "https://your-domain.com/files",
            "cloud_config": {}
        },
        "aws": {
            "link_type": "cloud",
            "base_url": "",
            "cloud_config": {
                "type": "aws",
                "bucket": "your-csv-json-bucket",
                "region": "us-east-1",
                "folder": "trajectories"
            }
        }
    }
    
    for name, config in link_configs.items():
        filename = f"link_config_{name}.json"
        with open(filename, "w") as f:
            json.dump(config, f, indent=2)
        print(f"   Created: {filename}")
    
    print("✅ Configuration files created")
    return True

def create_startup_scripts():
    """Create startup scripts for different platforms"""
    print("🚀 Creating startup scripts...")
    
    # Windows batch script
    windows_script = """@echo off
echo Starting Automated CSV-JSON System...
python automated_csv_json_system.py
pause
"""
    
    with open("start_system.bat", "w") as f:
        f.write(windows_script)
    print("   Created: start_system.bat (Windows)")
    
    # Unix shell script
    unix_script = """#!/bin/bash
echo "Starting Automated CSV-JSON System..."
python3 automated_csv_json_system.py
"""
    
    with open("start_system.sh", "w") as f:
        f.write(unix_script)
    
    # Make shell script executable
    try:
        os.chmod("start_system.sh", 0o755)
    except:
        pass  # Ignore on Windows
    
    print("   Created: start_system.sh (Linux/Mac)")
    
    print("✅ Startup scripts created")
    return True

def test_installation():
    """Test if the installation works"""
    print("🧪 Testing installation...")
    
    try:
        # Test imports
        import pandas
        import watchdog
        import flask
        print("   ✅ All modules can be imported")
        
        # Test CSV conversion
        from csv_to_json import batch_transform
        print("   ✅ CSV conversion module works")
        
        # Test file monitoring
        from file_monitor import FileMonitor
        print("   ✅ File monitoring module works")
        
        # Test spreadsheet integration
        from spreadsheet_integration import SpreadsheetManager
        print("   ✅ Spreadsheet integration works")
        
        print("✅ Installation test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def print_next_steps():
    """Print instructions for next steps"""
    print("\n" + "="*60)
    print("🎉 INSTALLATION COMPLETED SUCCESSFULLY!")
    print("="*60)
    print("📋 NEXT STEPS:")
    print()
    print("1. 🔧 Configure the system:")
    print("   - Edit 'system_config.json' for your needs")
    print("   - Choose link configuration from link_config_*.json files")
    print()
    print("2. 🚀 Start the system:")
    print("   - Windows: Double-click 'start_system.bat'")
    print("   - Linux/Mac: Run './start_system.sh'")
    print("   - Manual: python automated_csv_json_system.py")
    print()
    print("3. 🌐 Access the web interface:")
    print("   - Open http://localhost:5000 in your browser")
    print("   - Upload CSV files or copy them to 'input_csvs' folder")
    print()
    print("4. 📊 Check the spreadsheet:")
    print("   - Open 'tracking_spreadsheet.csv' to see file links")
    print()
    print("5. 📁 File locations:")
    print(f"   - Input CSV files: {Path('input_csvs').absolute()}")
    print(f"   - Output JSON files: {Path('output_jsons').absolute()}")
    print(f"   - Tracking spreadsheet: {Path('tracking_spreadsheet.csv').absolute()}")
    print()
    print("="*60)
    print("📖 For more information, check the README.md file")
    print("🐛 For issues, check the logs in automated_system.log")
    print("="*60)

def main():
    """Main installation function"""
    print("🔧 Automated CSV-JSON System Installer")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Installation failed at dependency installation")
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("❌ Installation failed at directory creation")
        sys.exit(1)
    
    # Create configuration files
    if not create_sample_config():
        print("❌ Installation failed at configuration creation")
        sys.exit(1)
    
    # Create startup scripts
    if not create_startup_scripts():
        print("❌ Installation failed at startup script creation")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("❌ Installation test failed")
        sys.exit(1)
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
