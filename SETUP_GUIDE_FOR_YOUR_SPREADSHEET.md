# 🔧 **Setup Guide for Your Existing Spreadsheet**

## ✅ **What We've Created for You**

Your custom CSV-to-JSON monitoring system is now ready! Here's what we built:

### **Files Created:**
1. **`custom_monitor_config.json`** - Configuration file mapping your spreadsheet columns
2. **`custom_spreadsheet_monitor.py`** - Custom monitor script for your spreadsheet
3. **`configure_for_existing_spreadsheet.py`** - Setup script for future spreadsheets

## 🚀 **How to Use with Your Own Spreadsheet**

### **Step 1: Prepare Your Spreadsheet**

Your spreadsheet needs these **3 required columns** (you can name them anything):

| Purpose | Example Column Names | Required? |
|---------|---------------------|-----------|
| **Unique ID** | ID, Entry_ID, Record_ID | ✅ Required |
| **CSV File Path** | Link to CSV, CSV_Path, File_Location | ✅ Required |
| **JSON File Path** | Link to JSON, JSON_Path, Output_File | ✅ Required |

**Optional columns** (recommended):
- Date, Timestamp
- Annotator, User, Researcher
- Category, Type, Classification
- Status, Progress
- Notes, Comments

### **Step 2: Run the Configuration Script**

```bash
python configure_for_existing_spreadsheet.py
```

**The script will ask you:**

1. **📊 Path to your spreadsheet**: 
   - Example: `my_data_tracking.xlsx` or `C:\path\to\my_spreadsheet.csv`

2. **🔧 Column mapping**: 
   - Map your column names to the required functions
   - You can enter column numbers (1, 2, 3) or partial names

**Example interaction:**
```
📊 Enter path to your existing spreadsheet: my_research_data.xlsx

📋 Found 6 columns:
   1. Date_Added
   2. Research_ID  
   3. Scientist_Name
   4. CSV_File_Location
   5. JSON_Output_Link
   6. Data_Category

ID column (unique identifier): 2
✅ Mapped to 'Research_ID'

CSV file path column: 4  
✅ Mapped to 'CSV_File_Location'

JSON file path column: 5
✅ Mapped to 'JSON_Output_Link'
```

### **Step 3: Start Monitoring**

After configuration, you'll have:
- **`custom_monitor_config.json`** - Your spreadsheet configuration
- **`custom_spreadsheet_monitor.py`** - Your custom monitor

**Start the monitor:**

```bash
# Continuous monitoring (recommended)
python custom_spreadsheet_monitor.py --continuous

# Process once and exit
python custom_spreadsheet_monitor.py

# Custom check interval (30 seconds)
python custom_spreadsheet_monitor.py --continuous --interval 30
```

## 📋 **Workflow for Your Annotators**

### **Simple 3-Step Process:**

1. **Open your spreadsheet** (Excel or CSV)
2. **Add new row** with:
   - **Unique ID**: Any unique identifier (ENTRY001, REC_2024_001, etc.)
   - **CSV File Path**: Path to the CSV file (input_csvs/my_data.csv)
   - **JSON File Path**: Leave EMPTY (will be auto-filled)
   - **Other columns**: Fill as needed (date, annotator, category, etc.)
3. **Save the spreadsheet**

**That's it!** The monitor will:
- Detect the new entry
- Convert CSV to JSON
- Update the JSON file path automatically

### **Example Before/After:**

**Before (Annotator adds):**
| Research_ID | CSV_File_Location | JSON_Output_Link | Scientist_Name |
|-------------|-------------------|------------------|----------------|
| REC_001 | data/conversation_log.csv | | Dr. Smith |

**After (System processes):**
| Research_ID | CSV_File_Location | JSON_Output_Link | Scientist_Name |
|-------------|-------------------|------------------|----------------|
| REC_001 | data/conversation_log.csv | **output_jsons/conversation_log.json** | Dr. Smith |

## 🔧 **Configuration Details**

### **Your Current Configuration:**
```json
{
  "spreadsheet_path": "tracking_spreadsheet.csv",
  "input_dir": "input_csvs",
  "output_dir": "output_jsons", 
  "check_interval": 10,
  "column_mapping": {
    "id_column": "ID",
    "csv_column": "Link to CSV", 
    "json_column": "Link to JSON",
    "date_column": "Date",
    "annotator_column": "Annotator",
    "category_column": "Category",
    "status_column": "Status"
  }
}
```

### **To Use with Different Spreadsheet:**
1. Run `python configure_for_existing_spreadsheet.py` again
2. Point to your new spreadsheet
3. Map the columns appropriately
4. Start the new monitor

## 🎯 **Key Features**

- ✅ **Works with Excel (.xlsx) and CSV files**
- ✅ **Flexible column mapping** - use any column names
- ✅ **Automatic JSON generation** - no manual work needed
- ✅ **Real-time monitoring** - processes new entries immediately
- ✅ **Error handling** - graceful handling of missing files
- ✅ **Preserves existing data** - only processes empty JSON cells
- ✅ **Logging** - detailed logs for troubleshooting

## 🚨 **Important Notes**

### **For Your CSV Files:**
- Must be in the correct format (same structure as your existing files)
- Should contain columns: id, session_id, turn_id, event_type, role, content, etc.
- Place CSV files in accessible location (recommend `input_csvs/` folder)

### **For Your Spreadsheet:**
- Keep the monitor running for automatic processing
- Use unique IDs for each entry
- CSV file paths should be relative to the project folder
- JSON column will be automatically updated

### **File Paths:**
- **Relative paths**: `input_csvs/my_file.csv` (recommended)
- **Absolute paths**: `C:\full\path\to\file.csv` (also works)
- **Network paths**: `\\server\share\file.csv` (if accessible)

## 🔄 **Testing Your Setup**

1. **Add a test entry** to your spreadsheet:
   ```
   ID: TEST_001
   CSV Path: input_csvs/test_file.csv  
   JSON Path: (leave empty)
   ```

2. **Run the monitor once**:
   ```bash
   python custom_spreadsheet_monitor.py
   ```

3. **Check results**:
   - JSON file should be created in `output_jsons/`
   - Spreadsheet should be updated with JSON path
   - Check console logs for any errors

## 🎉 **You're All Set!**

Your automated CSV-to-JSON system is now configured for your existing spreadsheet. Annotators can simply add CSV file paths, and the system will handle everything else automatically!

**No more manual downloading and pasting - everything is automated!** 🚀
