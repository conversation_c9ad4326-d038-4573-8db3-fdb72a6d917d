# 📊 Spreadsheet-Integrated CSV-to-JSON Workflow

## 🎯 **What You Asked For**

You want annotators to:
1. Paste CSV file paths into your spreadsheet
2. Automatically get JSON files generated
3. Have JSON file links appear in the spreadsheet

## ✨ **Perfect Solution: Simple Spreadsheet Monitor**

I've created `simple_spreadsheet_monitor.py` that does exactly this!

## 🚀 **Quick Setup**

### 1. Install Dependencies
```bash
pip install pandas openpyxl
```

### 2. Create Your Spreadsheet Structure
Your spreadsheet needs these columns:
- **Date** - When the entry was added
- **ID** - Unique identifier for each entry
- **Annotator** - Name of the person who added the CSV
- **Link to CSV** - Path to the CSV file (this is where annotators paste)
- **Link to JSON** - JSON file link (automatically filled)
- **Category** - Category of the data

### 3. Create Example Spreadsheet
```bash
python simple_spreadsheet_monitor.py --create-example
```

This creates `example_tracking_spreadsheet.csv` with the correct structure.

## 📋 **How It Works**

### For Annotators:
1. **Open your tracking spreadsheet** (Excel or CSV)
2. **Add a new row** with:
   - Date: Current date
   - ID: Unique identifier (e.g., ENTRY001, ENTRY002)
   - Annotator: Your name
   - Link to CSV: **Paste the path to your CSV file here**
   - Category: Select appropriate category
   - **Leave "Link to JSON" empty**
3. **Save the spreadsheet**

### For System Administrator:
1. **Run the monitor** to automatically process new entries:
   ```bash
   # Process once
   python simple_spreadsheet_monitor.py --spreadsheet your_spreadsheet.csv
   
   # Or run continuously (checks every 10 seconds)
   python simple_spreadsheet_monitor.py --spreadsheet your_spreadsheet.csv --continuous
   ```

2. **The system automatically**:
   - Detects new CSV entries
   - Converts CSV files to JSON
   - Updates the "Link to JSON" column
   - Saves the updated spreadsheet

## 📁 **File Organization**

```
Your Project/
├── tracking_spreadsheet.csv          # Your main tracking spreadsheet
├── input_csvs/                       # Where CSV files are stored
│   ├── MSA024.csv
│   ├── MSA29.csv
│   └── chat-session-logs-*.csv
├── output_jsons/                     # Auto-generated JSON files
│   ├── MSA024.json
│   ├── MSA29.json
│   └── chat-session-logs-*.json
├── simple_spreadsheet_monitor.py     # The monitoring script
└── csv_to_json.py                    # Conversion logic
```

## 📊 **Example Spreadsheet**

| Date | ID | Annotator | Link to CSV | Link to JSON | Category |
|------|----|-----------|-----------|-----------|---------| 
| 2025-10-08 10:00 | ENTRY001 | Alice | input_csvs/MSA024.csv | output_jsons/MSA024.json | conversation_logs |
| 2025-10-08 10:15 | ENTRY002 | Bob | input_csvs/MSA29.csv | | tool_trajectories |
| 2025-10-08 10:30 | ENTRY003 | Charlie | input_csvs/chat-session-logs-123.csv | | user_interactions |

**After running the monitor**, the empty "Link to JSON" cells are automatically filled!

## 🔄 **Workflow Steps**

### Step 1: Annotator Adds CSV Entry
```
Annotator opens spreadsheet → Adds new row → Pastes CSV path → Saves
```

### Step 2: System Processes Entry
```
Monitor detects new entry → Converts CSV to JSON → Updates spreadsheet
```

### Step 3: Results Available
```
JSON file created → Spreadsheet updated → Links available for download
```

## ⚙️ **Command Options**

```bash
# Process spreadsheet once
python simple_spreadsheet_monitor.py --spreadsheet tracking_spreadsheet.csv

# Run continuous monitoring (checks every 10 seconds)
python simple_spreadsheet_monitor.py --spreadsheet tracking_spreadsheet.csv --continuous

# Change check interval to 30 seconds
python simple_spreadsheet_monitor.py --spreadsheet tracking_spreadsheet.csv --continuous --interval 30

# Create example spreadsheet
python simple_spreadsheet_monitor.py --create-example
```

## 🎯 **Benefits of This Approach**

1. **No Web Interface Needed** - Works directly with your existing spreadsheet
2. **Simple for Annotators** - Just paste CSV paths and save
3. **Automatic Processing** - JSON files generated automatically
4. **Integrated Tracking** - Everything in one spreadsheet
5. **Flexible** - Works with Excel (.xlsx) or CSV files
6. **Real-time** - Continuous monitoring option available

## 🔧 **Customization**

You can modify the column names in `simple_spreadsheet_monitor.py`:

```python
# Change these if your spreadsheet uses different column names
required_columns = ['ID', 'Link to CSV', 'Link to JSON']
```

## 🚨 **Troubleshooting**

### CSV File Not Found
- Make sure the path in "Link to CSV" is correct
- Use relative paths like `input_csvs/filename.csv`
- Or absolute paths like `C:/path/to/file.csv`

### JSON Not Generated
- Check the log messages for errors
- Ensure CSV file format is correct
- Verify the CSV file is not corrupted

### Spreadsheet Not Updating
- Make sure you have write permissions
- Close Excel if the file is open there
- Check the log for error messages

## 🎉 **Result**

Your annotators can now:
1. **Paste CSV file paths** into the spreadsheet
2. **Save the spreadsheet**
3. **Automatically get JSON files** generated
4. **See JSON links** appear in the spreadsheet

**No web interface needed - everything works through your existing spreadsheet workflow!**
