#!/usr/bin/env python3
"""
Demonstration script for the Automated CSV-JSON System
Shows how the system works with sample data
"""

import os
import sys
import time
import json
import shutil
from pathlib import Path
import pandas as pd
from datetime import datetime

def create_sample_csv_files():
    """Create sample CSV files for demonstration"""
    print("📁 Creating sample CSV files...")
    
    # Sample conversation data
    sample_data = [
        {
            'id': 1,
            'session_id': 'demo-session-001',
            'turn_id': 'turn-001',
            'timestamp': '2025-10-07T12:00:00Z',
            'event_type': 'user_message',
            'role': 'user',
            'content': 'What is the weather like in New York today?',
            'tool_name': '',
            'original_args': '',
            'execution_result': ''
        },
        {
            'id': 2,
            'session_id': 'demo-session-001',
            'turn_id': 'turn-001',
            'timestamp': '2025-10-07T12:00:01Z',
            'event_type': 'reasoning_completed',
            'role': 'system',
            'content': '',
            'tool_name': '',
            'original_args': '',
            'execution_result': 'I need to get weather information for New York. I will use the weather tool to fetch current conditions.'
        },
        {
            'id': 3,
            'session_id': 'demo-session-001',
            'turn_id': 'turn-001',
            'timestamp': '2025-10-07T12:00:02Z',
            'event_type': 'tool_call_interrupt',
            'role': 'assistant',
            'content': '',
            'tool_name': 'weather',
            'original_args': '{"query": "New York weather today"}',
            'execution_result': ''
        },
        {
            'id': 4,
            'session_id': 'demo-session-001',
            'turn_id': 'turn-001',
            'timestamp': '2025-10-07T12:00:03Z',
            'event_type': 'tool_execution_approved',
            'role': 'tool',
            'content': '',
            'tool_name': 'weather',
            'original_args': '',
            'execution_result': '{"temperature": "22°C", "condition": "Sunny", "humidity": "65%", "wind": "10 mph"}'
        },
        {
            'id': 5,
            'session_id': 'demo-session-001',
            'turn_id': 'turn-001',
            'timestamp': '2025-10-07T12:00:04Z',
            'event_type': 'ai_response',
            'role': 'assistant',
            'content': 'The weather in New York today is sunny with a temperature of 22°C. The humidity is at 65% and there is a light wind of 10 mph. It is a beautiful day!',
            'tool_name': '',
            'original_args': '',
            'execution_result': ''
        }
    ]
    
    # Create sample files
    input_dir = Path('input_csvs')
    input_dir.mkdir(exist_ok=True)
    
    # Create multiple sample files
    samples = [
        ('demo_weather_query', sample_data),
        ('demo_annotator_alice', sample_data.copy()),
        ('demo_conversation_log', sample_data.copy())
    ]
    
    for filename, data in samples:
        # Modify data slightly for each file
        for i, row in enumerate(data):
            row['session_id'] = f'{filename}-session'
            row['id'] = i + 1
        
        df = pd.DataFrame(data)
        csv_file = input_dir / f'{filename}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        df.to_csv(csv_file, index=False)
        print(f"   Created: {csv_file}")
    
    print("✅ Sample CSV files created")
    return len(samples)

def demonstrate_manual_conversion():
    """Demonstrate manual CSV to JSON conversion"""
    print("\n🔄 Demonstrating manual conversion...")
    
    from csv_to_json import batch_transform
    
    # Run batch conversion
    batch_transform("input_csvs", "output_jsons")
    
    # Check results
    output_dir = Path('output_jsons')
    json_files = list(output_dir.glob('*.json'))
    
    print(f"✅ Converted {len(json_files)} files to JSON")
    
    # Show sample output
    if json_files:
        sample_file = json_files[0]
        print(f"\n📄 Sample output from {sample_file.name}:")
        with open(sample_file, 'r') as f:
            data = json.load(f)
        
        print(f"   Messages: {len(data['messages'])}")
        for i, msg in enumerate(data['messages'][:3]):  # Show first 3 messages
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')[:50] + '...' if len(msg.get('content', '')) > 50 else msg.get('content', '')
            print(f"   {i+1}. {role}: {content}")
        
        if len(data['messages']) > 3:
            print(f"   ... and {len(data['messages']) - 3} more messages")

def demonstrate_spreadsheet_integration():
    """Demonstrate spreadsheet integration"""
    print("\n📊 Demonstrating spreadsheet integration...")
    
    from spreadsheet_integration import AutomatedSpreadsheetUpdater
    
    # Initialize spreadsheet updater
    updater = AutomatedSpreadsheetUpdater("demo_tracking_spreadsheet.csv")
    
    if updater.initialize():
        print("✅ Spreadsheet initialized")
        
        # Add some sample entries
        csv_files = list(Path('input_csvs').glob('*.csv'))
        json_files = list(Path('output_jsons').glob('*.json'))
        
        for csv_file in csv_files[:3]:  # Process first 3 files
            # Find corresponding JSON file
            json_file = Path('output_jsons') / f"{csv_file.stem}.json"
            
            if json_file.exists():
                # Extract annotator from filename
                annotator = "Demo_User"
                if "alice" in csv_file.name:
                    annotator = "Alice"
                elif "weather" in csv_file.name:
                    annotator = "WeatherBot"
                
                # Determine category
                category = "conversation_logs"
                if "tool" in csv_file.name:
                    category = "tool_trajectories"
                
                success = updater.process_new_files(csv_file, json_file, annotator, category)
                if success:
                    print(f"   Added: {csv_file.name} -> {json_file.name}")
        
        # Show spreadsheet stats
        stats = updater.manager.get_stats()
        print(f"📈 Spreadsheet stats: {stats}")
        
        # Save the spreadsheet
        updater.manager.save_spreadsheet()
        print("💾 Spreadsheet saved")
    else:
        print("❌ Failed to initialize spreadsheet")

def demonstrate_link_generation():
    """Demonstrate link generation"""
    print("\n🔗 Demonstrating link generation...")
    
    from link_generator import LinkGenerator
    
    # Test different link types
    configs = [
        {"link_type": "relative", "name": "Relative paths"},
        {"link_type": "web", "base_url": "https://example.com/files", "name": "Web URLs"},
        {"link_type": "cloud", "cloud_config": {"type": "aws", "bucket": "demo-bucket"}, "name": "Cloud storage"}
    ]
    
    csv_file = Path("input_csvs/demo_file.csv")
    json_file = Path("output_jsons/demo_file.json")
    
    for config in configs:
        name = config.pop("name")
        generator = LinkGenerator(config)
        
        csv_link = generator.generate_csv_link(csv_file)
        json_link = generator.generate_json_link(json_file)
        
        print(f"   {name}:")
        print(f"     CSV:  {csv_link}")
        print(f"     JSON: {json_link}")

def demonstrate_file_monitoring():
    """Demonstrate file monitoring (simulation)"""
    print("\n👁️ Demonstrating file monitoring...")
    print("   (This would normally watch for new files automatically)")
    
    from file_monitor import FileMonitor
    
    # Create a callback function
    processed_files = []
    
    def demo_callback(csv_file, json_file):
        processed_files.append((csv_file.name, json_file.name))
        print(f"   📝 Processed: {csv_file.name} -> {json_file.name}")
    
    # Initialize monitor
    monitor = FileMonitor(
        input_dir="input_csvs",
        output_dir="output_jsons",
        callback=demo_callback
    )
    
    print("✅ File monitor initialized")
    print("   In real usage, this would:")
    print("   - Watch the input_csvs folder")
    print("   - Automatically convert new CSV files")
    print("   - Update the spreadsheet")
    print("   - Generate appropriate links")

def show_web_interface_info():
    """Show information about the web interface"""
    print("\n🌐 Web Interface Information...")
    print("   To start the web interface:")
    print("   1. Run: python web_interface.py")
    print("   2. Open: http://localhost:5000")
    print("   3. Upload CSV files via drag-and-drop")
    print("   4. Files are automatically processed")
    print("   5. Download links are provided")

def cleanup_demo_files():
    """Clean up demo files"""
    print("\n🧹 Cleaning up demo files...")
    
    # Remove demo files but keep the structure
    demo_files = [
        "demo_tracking_spreadsheet.csv",
        "link_config_local.json",
        "link_config_web.json",
        "link_config_aws.json"
    ]
    
    for file in demo_files:
        if Path(file).exists():
            Path(file).unlink()
            print(f"   Removed: {file}")
    
    # Clean up input files with demo prefix
    input_dir = Path('input_csvs')
    if input_dir.exists():
        demo_csvs = list(input_dir.glob('demo_*.csv'))
        for csv_file in demo_csvs:
            csv_file.unlink()
            print(f"   Removed: {csv_file}")
    
    # Clean up corresponding JSON files
    output_dir = Path('output_jsons')
    if output_dir.exists():
        demo_jsons = list(output_dir.glob('demo_*.json'))
        for json_file in demo_jsons:
            json_file.unlink()
            print(f"   Removed: {json_file}")
    
    print("✅ Demo cleanup completed")

def main():
    """Main demonstration function"""
    print("🎬 Automated CSV-JSON System Demonstration")
    print("=" * 60)
    
    try:
        # Create sample data
        num_files = create_sample_csv_files()
        
        # Demonstrate manual conversion
        demonstrate_manual_conversion()
        
        # Demonstrate spreadsheet integration
        demonstrate_spreadsheet_integration()
        
        # Demonstrate link generation
        demonstrate_link_generation()
        
        # Demonstrate file monitoring
        demonstrate_file_monitoring()
        
        # Show web interface info
        show_web_interface_info()
        
        print("\n" + "=" * 60)
        print("🎉 DEMONSTRATION COMPLETED!")
        print("=" * 60)
        print("📋 What was demonstrated:")
        print(f"   ✅ Created {num_files} sample CSV files")
        print("   ✅ Converted CSV files to JSON format")
        print("   ✅ Updated tracking spreadsheet")
        print("   ✅ Generated different types of file links")
        print("   ✅ Showed file monitoring capabilities")
        print("   ✅ Explained web interface usage")
        print()
        print("🚀 To start the full automated system:")
        print("   python automated_csv_json_system.py")
        print()
        print("🌐 To start just the web interface:")
        print("   python web_interface.py")
        print()
        
        # Ask if user wants to clean up
        response = input("🧹 Clean up demo files? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            cleanup_demo_files()
        else:
            print("📁 Demo files kept for your inspection")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
