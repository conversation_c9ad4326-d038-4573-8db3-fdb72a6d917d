#!/usr/bin/env python3
"""
Simple script to run the CSV to JSON converter
Usage: python run_csv_to_json.py
"""

from csv_to_json import batch_transform
import os

def main():
    """Run the CSV to JSON converter with default settings"""
    input_folder = "input_csvs"
    output_folder = "output_jsons"

    # Check if input folder exists
    if not os.path.exists(input_folder):
        print(f"❌ Input folder '{input_folder}' not found!")
        print("Please create the folder and add your CSV files.")
        return

    # Create output folder if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)

    print(f"🔄 Converting CSV files from '{input_folder}' to '{output_folder}'...")

    try:
        batch_transform(input_folder, output_folder)
        print("✅ Conversion completed successfully!")
    except Exception as e:
        print(f"❌ Error during conversion: {e}")

if __name__ == "__main__":
    main()
