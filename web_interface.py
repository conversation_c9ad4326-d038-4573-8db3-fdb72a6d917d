#!/usr/bin/env python3
"""
Web interface for CSV file uploads and automatic JSON conversion
Simple Flask-based interface for annotators
"""

from flask import Flask, request, render_template_string, jsonify, send_file, redirect, url_for, flash
import os
from pathlib import Path
import logging
from werkzeug.utils import secure_filename
from datetime import datetime
import json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'  # Change this in production

# Configuration
UPLOAD_FOLDER = Path('input_csvs')
OUTPUT_FOLDER = Path('output_jsons')
ALLOWED_EXTENSIONS = {'csv'}

# Create directories if they don't exist
UPLOAD_FOLDER.mkdir(exist_ok=True)
OUTPUT_FOLDER.mkdir(exist_ok=True)

def allowed_file(filename):
    """Check if file has allowed extension"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# HTML Templates
UPLOAD_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV to JSON Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .submit-btn {
            background-color: #28a745;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .submit-btn:hover {
            background-color: #218838;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-list {
            margin-top: 30px;
        }
        .file-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .file-links {
            margin-top: 10px;
        }
        .file-links a {
            color: #007bff;
            text-decoration: none;
            margin-right: 15px;
        }
        .file-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 CSV to JSON Converter</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="post" enctype="multipart/form-data" id="uploadForm">
            <div class="upload-area" id="uploadArea">
                <p>📁 Drag and drop your CSV file here or click to browse</p>
                <input type="file" name="file" id="fileInput" accept=".csv" required>
                <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    Choose CSV File
                </button>
            </div>
            
            <div class="form-group">
                <label for="annotator">Annotator Name:</label>
                <input type="text" id="annotator" name="annotator" placeholder="Enter your name">
            </div>
            
            <div class="form-group">
                <label for="category">Category:</label>
                <select id="category" name="category">
                    <option value="">Select category</option>
                    <option value="conversation_logs">Conversation Logs</option>
                    <option value="tool_trajectories">Tool Trajectories</option>
                    <option value="user_interactions">User Interactions</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <button type="submit" class="submit-btn">🚀 Upload and Convert</button>
        </form>
        
        <div class="file-list">
            <h3>📋 Recent Files</h3>
            <div id="recentFiles">
                {% for file_info in recent_files %}
                <div class="file-item">
                    <strong>{{ file_info.name }}</strong>
                    <br>
                    <small>Uploaded: {{ file_info.date }}</small>
                    <div class="file-links">
                        <a href="{{ url_for('download_file', filename=file_info.csv_file) }}">📄 Download CSV</a>
                        {% if file_info.json_file %}
                        <a href="{{ url_for('download_file', filename=file_info.json_file) }}">📋 Download JSON</a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        // Drag and drop functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateFileName();
            }
        });
        
        fileInput.addEventListener('change', updateFileName);
        
        function updateFileName() {
            const fileName = fileInput.files[0]?.name;
            if (fileName) {
                uploadArea.innerHTML = `
                    <p>✅ Selected: ${fileName}</p>
                    <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose Different File
                    </button>
                `;
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Main upload page"""
    # Get recent files
    recent_files = get_recent_files()
    return render_template_string(UPLOAD_TEMPLATE, recent_files=recent_files)

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload"""
    try:
        if 'file' not in request.files:
            flash('No file selected', 'error')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected', 'error')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            # Secure the filename
            filename = secure_filename(file.filename)
            
            # Add timestamp to avoid conflicts
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            name, ext = os.path.splitext(filename)
            filename = f"{name}_{timestamp}{ext}"
            
            # Save the file
            file_path = UPLOAD_FOLDER / filename
            file.save(str(file_path))
            
            # Get additional info
            annotator = request.form.get('annotator', '')
            category = request.form.get('category', '')
            
            # Log the upload
            logger.info(f"File uploaded: {filename} by {annotator}")
            
            # The file monitor will automatically process this file
            flash(f'File uploaded successfully! Processing will begin automatically.', 'success')
            
            return redirect(url_for('index'))
        else:
            flash('Invalid file type. Please upload a CSV file.', 'error')
            return redirect(request.url)
            
    except Exception as e:
        logger.error(f"Upload error: {e}")
        flash(f'Upload failed: {str(e)}', 'error')
        return redirect(request.url)

@app.route('/download/<filename>')
def download_file(filename):
    """Download a file"""
    try:
        # Check in both input and output directories
        csv_path = UPLOAD_FOLDER / filename
        json_path = OUTPUT_FOLDER / filename
        
        if csv_path.exists():
            return send_file(csv_path, as_attachment=True)
        elif json_path.exists():
            return send_file(json_path, as_attachment=True)
        else:
            flash('File not found', 'error')
            return redirect(url_for('index'))
            
    except Exception as e:
        logger.error(f"Download error: {e}")
        flash(f'Download failed: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/api/status')
def api_status():
    """API endpoint for status"""
    try:
        csv_count = len(list(UPLOAD_FOLDER.glob('*.csv')))
        json_count = len(list(OUTPUT_FOLDER.glob('*.json')))
        
        return jsonify({
            'status': 'running',
            'csv_files': csv_count,
            'json_files': json_count,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def get_recent_files(limit=10):
    """Get list of recent files"""
    try:
        files = []
        
        # Get CSV files with their modification times
        for csv_file in UPLOAD_FOLDER.glob('*.csv'):
            stat = csv_file.stat()
            
            # Check if corresponding JSON exists
            json_file = OUTPUT_FOLDER / f"{csv_file.stem}.json"
            json_exists = json_file.exists()
            
            files.append({
                'name': csv_file.stem,
                'csv_file': csv_file.name,
                'json_file': json_file.name if json_exists else None,
                'date': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'timestamp': stat.st_mtime
            })
        
        # Sort by timestamp (newest first) and limit
        files.sort(key=lambda x: x['timestamp'], reverse=True)
        return files[:limit]
        
    except Exception as e:
        logger.error(f"Error getting recent files: {e}")
        return []

def main():
    """Run the web interface"""
    print("🌐 Starting CSV to JSON Web Interface...")
    print("📁 Upload folder:", UPLOAD_FOLDER.absolute())
    print("📄 Output folder:", OUTPUT_FOLDER.absolute())
    print("🔗 Access the interface at: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)

if __name__ == '__main__':
    main()
